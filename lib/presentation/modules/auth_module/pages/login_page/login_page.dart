import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/login_page/provider/login_provider.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/login_page/widgets/language_popup_widget.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// Login Screen
class LoginPage extends StatelessWidget {
  /// Login Screen
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<LoginProvider>(
      create: (context) => LoginProvider(),
      child: Consumer<LoginProvider>(
        builder: (context, loginProvider, _) {
          return Scaffold(
            appBar: const CustomAppBar(
              title: '',
              canPop: false,
              backgroundColor: AppColors.white,
              actions: [
                LanguagePopupWidget(),
              ],
            ),
            body: GestureDetector(
              onTap: AppCommonFunctions.closeKeyboard,
              child: ValueListenableBuilder(
                valueListenable: loginProvider.isShowLoader,
                builder: (context, isLoading, child) {
                  return AppLoader(
                    isShowLoader: isLoading,
                    child: child!,
                  );
                },
                child: SingleChildScrollView(
                  child: AppPadding.symmetric(
                    horizontal: AppSize.appPadding,
                    child: Column(
                      children: [
                        SizedBox(
                          height: context.height * 0.4,
                          width: double.infinity,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: AppSize.h64,
                                child: AppAssets.imagesLogo.image(),
                              ),
                              SizedBox(height: AppSize.h30),
                              Text(
                                context.l10n.ultimateStressFreeCarTransport,
                                textAlign: TextAlign.center,
                                style: context.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w400,
                                  fontSize: AppSize.sp16,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Form(
                          key: loginProvider.formKeyLogin,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppTextFormField(
                                title: context.l10n.email,
                                hintText: context.l10n.enterYourEmail,
                                keyboardType: TextInputType.emailAddress,
                                fillColor: AppColors.ffF8F9FA,
                                controller: loginProvider.emailController,
                                textAction: TextInputAction.next,
                                validator: (p0) => emailValidator().call(p0),
                              ),
                              Gap(AppSize.h30),
                              ValueListenableBuilder(
                                valueListenable: loginProvider.isPasswordShow,
                                builder: (context, isPasswordShow, _) {
                                  return AppTextFormField(
                                    title: context.l10n.password,
                                    hintText: context.l10n.enterYourPassword,
                                    fillColor: AppColors.ffF8F9FA,
                                    obscureText:
                                        !loginProvider.isPasswordShow.value,
                                    controller:
                                        loginProvider.passwordController,
                                    validator: (p0) =>
                                        passwordValidator().call(p0),
                                    suffixIcon: GestureDetector(
                                      onTap: () {
                                        loginProvider.isPasswordShow.value =
                                            !loginProvider.isPasswordShow.value;
                                      },
                                      child: Icon(
                                        isPasswordShow
                                            ? Icons.visibility_outlined
                                            : Icons.visibility_off_outlined,
                                      ),
                                    ),
                                  );
                                },
                              ),
                              Gap(AppSize.h8),
                              Align(
                                alignment: Alignment.centerRight,
                                child: InkWell(
                                  onTap: () {
                                    AppNavigationService.pushNamed(
                                      context,
                                      AppRoutes.authForgotPasswordScreen,
                                    );
                                  },
                                  child: Text(
                                    context.l10n.forgotPasswordQuestion,
                                    style:
                                        context.textTheme.bodyLarge?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      fontSize: AppSize.sp14,
                                      color: AppColors.ff0087C7,
                                    ),
                                  ),
                                ),
                              ),
                              AppPadding(
                                top: AppSize.h42,
                                bottom: AppSize.h16,
                                child: AppButton(
                                  text: context.l10n.logIn,
                                  onPressed: () {
                                    if (loginProvider.formKeyLogin.currentState?.validate() ?? false) {
                                      loginProvider.loginAPICall(
                                        context: context,
                                      );
                                    }
                                  },
                                ),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    context.l10n.doNotHaveAccount,
                                    style:
                                        context.textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.w400,
                                      fontSize: AppSize.sp14,
                                      color: AppColors.black,
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      AppNavigationService.pushNamed(
                                        context,
                                        AppRoutes.authSignupScreen,
                                      );
                                    },
                                    child: Text(
                                      ' ${context.l10n.signUp}',
                                      style:
                                          context.textTheme.bodyLarge?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        fontSize: AppSize.sp14,
                                        color: AppColors.primaryColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Gap(AppSize.h10),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
