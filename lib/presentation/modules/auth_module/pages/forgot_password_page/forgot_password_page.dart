// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/forgot_password_page/provider/forgot_password_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class ForgotPasswordPage extends StatelessWidget {
  const ForgotPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: '',
        backgroundColor: AppColors.white,
      ),
      body: ChangeNotifierProvider(
        create: (context) => ForgotPasswordProvider(),
        child: Consumer<ForgotPasswordProvider>(
          builder: (context, forgetPasswordProvider, _) {
            return GestureDetector(
              onTap: AppCommonFunctions.closeKeyboard,
              child: ValueListenableBuilder(
                valueListenable: forgetPasswordProvider.isShowLoader,
                builder: (context, isLoader, child) {
                  return AppLoader(
                    isShowLoader: isLoader,
                    child: child!,
                  );
                },
                child: AppPadding.symmetric(
                  horizontal: AppSize.appPadding,
                  child: Form(
                    key: forgetPasswordProvider.formKeyForgotPassword,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap(AppSize.h30),
                        Text(
                          context.l10n.forgotPassword,
                          style: context.textTheme.headlineLarge?.copyWith(
                            fontSize: AppSize.sp24,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Gap(AppSize.h8),
                        SizedBox(
                          width: AppSize.w280,
                          child: Text(
                            context.l10n.resetPasswordInstructions,
                            style: context.textTheme.bodySmall?.copyWith(
                              fontSize: AppSize.sp16,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        AppPadding.symmetric(
                          vertical: AppSize.h30,
                          child: AppTextFormField(
                            title: context.l10n.email,
                            hintText: context.l10n.enterYourEmail,
                            keyboardType: TextInputType.emailAddress,
                            fillColor: AppColors.ffF8F9FA,
                            controller: forgetPasswordProvider.emailController,
                            validator: (p0) => emailValidator().call(p0),
                          ),
                        ),
                        Gap(AppSize.h10),
                        AppButton(
                          text: context.l10n.sendAnEmail,
                          onPressed: () {
                            if (forgetPasswordProvider.formKeyForgotPassword.currentState?.validate()?? false) {
                              forgetPasswordProvider.forgotPasswordAPIcall(
                                context: context,
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
