// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/reset_password_page/provider/reset_password_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/utils/validators/password_match_validator.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class ResetPasswordPage extends StatelessWidget {
  const ResetPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: '',
        canPop: false,
        backgroundColor: AppColors.white,
      ),
      body: ChangeNotifierProvider(
        create: (context) => ResetPasswordProvider(),
        child: Consumer<ResetPasswordProvider>(
          builder: (context, resetPasswordProvider, _) {
            return GestureDetector(
              onTap: AppCommonFunctions.closeKeyboard,
              child: ValueListenableBuilder(
                valueListenable: resetPasswordProvider.isShowLoader,
                builder: (context, isLoading, child) {
                  return AppLoader(
                    isShowLoader: isLoading,
                    child: child!,
                  );
                },
                child: AppPadding.symmetric(
                  horizontal: AppSize.appPadding,
                  child: Form(
                    key: resetPasswordProvider.formKeyResetPassword,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap(AppSize.h30),
                        Text(
                          context.l10n.setPassword,
                          style: context.textTheme.headlineLarge?.copyWith(
                            fontSize: AppSize.sp24,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Gap(AppSize.h8),
                        Text(
                          context.l10n.goAheadAndSetANewPassword,
                          style: context.textTheme.bodySmall?.copyWith(
                            fontSize: AppSize.sp16,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        AppPadding.symmetric(
                          vertical: AppSize.h30,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              //* Password
                              ValueListenableBuilder(
                                valueListenable:
                                    resetPasswordProvider.isPasswordShow,
                                builder: (context, isPasswordShow, _) {
                                  return AppTextFormField(
                                    title: context.l10n.password,
                                    hintText: context.l10n.enterYourPassword,
                                    textAction:TextInputAction.next,
                                    fillColor: AppColors.ffF8F9FA,
                                    obscureText: !resetPasswordProvider
                                        .isPasswordShow.value,
                                    controller: resetPasswordProvider
                                        .passwordController,
                                    validator: (p0) =>
                                        passwordValidator().call(p0),
                                    suffixIcon: GestureDetector(
                                      onTap: () {
                                        resetPasswordProvider
                                                .isPasswordShow.value =
                                            !resetPasswordProvider
                                                .isPasswordShow.value;
                                      },
                                      child: isPasswordShow
                                          ? const Icon(
                                              Icons.visibility_outlined,
                                            )
                                          : const Icon(
                                              Icons.visibility_off_outlined,
                                            ),
                                    ),
                                  );
                                },
                              ),
                              Gap(AppSize.h28),
                              //* Confirm Password
                              AppPadding(
                                top: AppSize.h4,
                                child: ValueListenableBuilder(
                                  valueListenable: resetPasswordProvider
                                      .isConfirmPasswordShow,
                                  builder: (context, isConfirmPasswordShow, _) {
                                    return AppTextFormField(
                                      title: context.l10n.confirmPassword,
                                      hintText: context.l10n.enterYourPassword,
                                      fillColor: AppColors.ffF8F9FA,
                                      obscureText: !resetPasswordProvider
                                          .isConfirmPasswordShow.value,
                                      controller: resetPasswordProvider
                                          .confirmPasswordController,
                                      validator: (value) =>
                                          ConfirmPasswordValidator(
                                        errorText: context
                                            .l10n.pleaseEnterConfirmPassword,
                                        password: resetPasswordProvider
                                            .passwordController.text,
                                      ).call(value),
                                      suffixIcon: GestureDetector(
                                        onTap: () {
                                          resetPasswordProvider
                                                  .isConfirmPasswordShow.value =
                                              !resetPasswordProvider
                                                  .isConfirmPasswordShow.value;
                                        },
                                        child: isConfirmPasswordShow
                                            ? const Icon(
                                                Icons.visibility_outlined,
                                              )
                                            : const Icon(
                                                Icons.visibility_off_outlined,
                                              ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        Gap(AppSize.h10),
                        AppButton(
                          text: context.l10n.changePassword,
                          onPressed: () {
                            if (resetPasswordProvider.formKeyResetPassword.currentState?.validate() ?? false) {
                              resetPasswordProvider.resetPasswordAPIcall(
                                context: context,
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
