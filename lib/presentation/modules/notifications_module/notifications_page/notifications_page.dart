
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/notifications_module/notification_provider.dart';
import 'package:transportmatch_provider/presentation/modules/notifications_module/notifications_page/models/notification_model.dart';
import 'package:transportmatch_provider/presentation/modules/notifications_module/notifications_page/widgets/notifications_widgets.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// Notifications screen ui
class NotificationsPage extends StatelessWidget {
  /// Constructor
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final isProvider =
        Injector.instance<AppDB>().userModel?.user?.role?.toLowerCase() ==
            UserType.Provider.name.toLowerCase();
    return ChangeNotifierProvider(
      create: (context) => NotificationProvider(),
      child: Builder(
        builder: (context) {
          final notificationProvider = context.read<NotificationProvider>();
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(
              canPop: isProvider?false:true,
              horizontalPadding: AppSize.w10,
              title: l10n.allNotifications,
            ),
            body: Selector<NotificationProvider,
                (bool, List<NotificationModel>, String?)>(
              selector: (p0, p1) =>
                  (p1.isShowLoader, p1.notificationList, p1.nextUrl),
              builder: (context, value, child) => AppLoader(
                isShowLoader: value.$1,
                child: EasyRefresh(
                  header: AppCommonFunctions.getLoadingHeader(),
                  footer: AppCommonFunctions.getLoadingFooter(),
                  controller: notificationProvider.refreshController,
                  onRefresh: notificationProvider.getNotification,
                  onLoad: value.$3.isNotEmptyAndNotNull
                      ? () => notificationProvider.getNotification(
                            isPagination: true,
                          )
                      : null,
                  child: !value.$1 && value.$2.isEmpty
                      ? ListView(
                        children: [
                          SizedBox(
                              height: MediaQuery.of(context).size.height * 0.75,
                              child: Center(child: Text(l10n.noNotificationsFound)),),
                        ],
                      )
                      : ListView.builder(
                          itemCount: value.$2.length,
                          padding:
                              EdgeInsets.symmetric(horizontal: AppSize.w16),
                          itemBuilder: (context, index) {
                            final notificationData = value.$2[index];
                            final createdAt = notificationData.createdAt;
                            final currentGroupLabel =
                                notificationProvider.getGroupLabel(createdAt!);

                            var showGroupHeader = false;
                            if (index == 0) {
                              showGroupHeader = true;
                            } else {
                              final previous = value.$2[index - 1];
                              final prevGroupLabel =
                                  notificationProvider.getGroupLabel(
                                previous.createdAt ?? DateTime.now(),
                              );
                              showGroupHeader =
                                  prevGroupLabel != currentGroupLabel;
                            }
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (showGroupHeader)
                                  Padding(
                                    padding: EdgeInsets.only(
                                      bottom: AppSize.h10,
                                    ),
                                    child: Text(
                                      currentGroupLabel,
                                      style: context.textTheme.titleMedium
                                          ?.copyWith(
                                        fontWeight: FontWeight.w400,
                                        fontSize: AppSize.sp16,
                                        color: AppColors.ff6C757D,
                                      ),
                                    ),
                                  ),
                                InkWell(
                                  onTap: notificationData.relatedObjectData !=
                                          null
                                      ? () => notificationProvider
                                              .onNotificationTap(
                                            notificationModel: notificationData,
                                            notificationData: notificationData
                                                    .relatedObjectData ??
                                                {},
                                            context: context,
                                            notificationType: notificationData
                                                    .notificationType ??
                                                NotificationType.TRIP.name,
                                          )
                                      : null,
                                  child: NotificationsWidgets(
                                    icon: switch (
                                            notificationData.notificationType) {
                                      /// DO NOT CHANGE THE NOTIFICATION TYPE
                                      ('BOOKING_DETAIL' || 'BOOKING') =>
                                        AppAssets.iconsDollar,
                                      'TRIP' => AppAssets.iconsTripColor,
                                      _ => AppAssets.iconsAccount,
                                    }
                                        .image(
                                      width: AppSize.w24,
                                      height: AppSize.h24,
                                    ),
                                    textNormal:
                                        notificationData.description ?? '',
                                    textBold: notificationData.title ?? '',
                                    time: AppCommonFunctions.timeAgoConverter(
                                      dateString: notificationData.createdAt
                                              ?.toIso8601String() ??
                                          DateTime.now().toIso8601String(),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
