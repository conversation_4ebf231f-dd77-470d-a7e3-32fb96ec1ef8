// ignore_for_file: avoid_bool_literals_in_conditional_expressions


import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/notifications_module/notifications_page/models/notification_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/add_checklist_page/models/add_checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/provider/checklist_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/pages/offer_price_page/models/offer_price_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/app_string.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class NotificationProvider extends ChangeNotifier {
  NotificationProvider() {
    getNotification(isWantShowLoader: true);
  }

  bool isClosed = false;
  List<NotificationModel> notificationList = [];
  final refreshController = EasyRefreshController();

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  String getGroupLabel(DateTime date) {
    final l10n = rootNavKey.currentContext!.l10n;

    final localDate = date.toLocal();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final notificationDate =
        DateTime(localDate.year, localDate.month, localDate.day);

    if (notificationDate == today) return l10n.today;
    if (notificationDate == yesterday) return l10n.yesterday;

    return DateFormat('EEE, d MMM yyyy').format(notificationDate);
  }

  bool isShowLoader = false;
  CancelToken? cancelToken;
  String? nextUrl;
  Future<void> getNotification({
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    if (isClosed) return;
    cancelToken?.cancel();
    cancelToken = CancelToken();
    if (isWantShowLoader) {
      isShowLoader = true;
      notify();
    }
    try {
      final response =
          await Injector.instance<AccountRepository>().getNotification(
        ApiRequest(
          path: isPagination
              ? nextUrl ?? EndPoints.notifications
              : EndPoints.notifications,
          cancelToken: cancelToken,
        ),
      );
      if (isClosed) return;
      if (isWantShowLoader) {
        isShowLoader = false;
        notify();
      }
      response.when(
        success: (data) {
          if (isClosed || (cancelToken?.isCancelled ?? true)) {
            return;
          }
          nextUrl = data.next;
          if (!isPagination) notificationList.clear();
          final dummyList = notificationList + (data.results ?? []);
          notificationList = dummyList;
          notify();
        },
        error: (error) {
          if (isClosed || (cancelToken?.isCancelled ?? true)) {
            return;
          }
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed) return;
      if (isWantShowLoader) {
        isShowLoader = false;
        notify();
      }
      if (cancelToken?.isCancelled ?? true) return;
      if (kDebugMode) '==>> $e <<=='.logE;
    }
  }

  Future<void> onNotificationTap({
    required Map<String, dynamic> notificationData,
    required NotificationModel notificationModel,
    required BuildContext context,
    required String notificationType,
  }) async {
    'notificationModel : ${notificationModel.toJson()}'.logD;
    final data = notificationData;
    '==>>> type: $data'.logE;
    switch (notificationType) {
      case AppStrings.checklist:
        await AppNavigationService.pushNamed(
          rootNavKey.currentContext!,
          AppRoutes.tripsAddChecklistScreen,
          extra: AddChecklistParams(
            checkListProvider: CheckListProvider(
              carId: (data[AppStrings.bookedCarId] as String?) ?? '',
            ),
            checkListId: int.tryParse(
              (data[AppStrings.relatedObjectId] as String?) ?? '',
            ),
            isFromNotification: true,
            isVerify: notificationData.containsKey(AppStrings.isVerified)
                ? notificationData[AppStrings.isVerified] == false
                : false,
          ),
          // ChangeNotifierProvider(
          //   create: (context) => CheckListProvider(
          //     carId: (data[AppStrings.bookedCarId] as String?) ?? '',
          //   ),
          //   builder: (context, child) {
          //     return AddChecklistScreen(
          //       checkListProvider: Provider.of<CheckListProvider>(context),
          //       checkListId: int.tryParse(
          //         (data[AppStrings.relatedObjectId] as String?) ?? '',
          //       ),
          //     );
          //   },
          // ),
        );
      // final chatRoom = ChatRoom.fromJson(data);
      // Navigator.pushNamed(
      //   AppNavigator.navigatorKey.currentContext!,
      //   AppRoutes.chatScreen,
      //   arguments: chatRoom,
      // );
      case AppStrings.bookingDetailStr ||
            AppStrings.tripStr ||
            AppStrings.bookingStr:
        '==>>> notification: $data \n ${data[AppStrings.bookingType]}'.logE;

        // Check if not_to_redirect is "True" - if so, don't redirect
        final notToRedirect =
            data[AppStrings.notToRedirect]?.toString().toLowerCase();
        if (notToRedirect == 'true') {
          return;
        }

        /// if notification is for exclusive trip, then show exclusive trip screen
        if (data[AppStrings.bookingStatus] ==
            NotificationType.EXCLUSIVE.name) {
          if (data[AppStrings.bookingType] == AppStrings.sentOfferStr) {
            await AppNavigationService.pushNamed(
              rootNavKey.currentContext!,
              AppRoutes.tripsOfferPriceScreen,
              extra: OfferPriceParams(
                tripId: int.tryParse(
                  (data[AppStrings.relatedObjectId] as String?) ?? '',
                ),
              ),
              // OfferPriceScreen(
              //   tripId: int.tryParse(
              //     (data[AppStrings.relatedObjectId] as String?) ?? '',
              //   ),
              // ),
            );
          } else {
            await AppNavigationService.pushNamed(
              rootNavKey.currentContext!,
              AppRoutes.tripsExclusiveRequestedScreen,
              extra: ExclusiveRequestedParams(
                exclusiveBooking: null,
                requestedTripProvider: RequestedTripProvider(),
                tripDataProvider: TripDataProvider(),
                tripId: int.tryParse(
                  (data[AppStrings.relatedObjectId] as String?) ?? '',
                ),
              ),
              // MultiProvider(
              //   providers: [
              //     ChangeNotifierProvider(
              //       create: (context) => RequestedTripProvider(),
              //     ),
              //     ChangeNotifierProvider(
              //       create: (context) => TripDataProvider(),
              //     ),
              //   ],
              //   builder: (context, child) => ExclusiveRequestedScreen(
              //     exclusiveBooking: null,
              //     tripId: booking[AppStrings.bookingId] as int?,
              //     requestedTripProvider:
              //         Provider.of<RequestedTripProvider>(context),
              //     tripDataProvider: Provider.of<TripDataProvider>(context),
              //   ),
              //   // child: ExclusiveRequestedScreen(
              //   //   exclusiveBooking: null,
              //   //   tripId: booking[AppStrings.bookingId] as int?,
              //   //   requestedTripProvider: widget.requestedTripProvider,
              //   //   tripDataProvider: widget.tripDataProvider,
              //   // ),
              // ),
            );
          }

          /// if notification is for sent offer, then show offer price screen
        } else {
          await AppNavigationService.pushNamed(
            rootNavKey.currentContext!,
            AppRoutes.tripsAllShipmentsScreen,
            extra: AllShipmentsParams(
              tripId: data[AppStrings.tripId] as int?,
              bookingId: data[AppStrings.bookingId] as int?,
              slots: num.tryParse((data[AppStrings.bookedSlot] as String?) ?? ''),
            ),
            // AllShipmentsScreen(
            //   tripId: booking[AppStrings.tripId] as int?,
            //   bookingId: booking[AppStrings.bookingId] as int?,
            // ),
          );
        }
            // related_object_data
    }
  }

  @override
  void dispose() {
    isClosed = true;
    cancelToken?.cancel();
    notificationList.clear();
    refreshController.dispose();
    super.dispose();
  }
}
