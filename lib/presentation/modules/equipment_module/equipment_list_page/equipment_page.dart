// ignore_for_file: public_member_api_docs


import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/models/equipment_add_update_params.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/provider/equipment_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class EquipmentPage extends StatelessWidget {
  const EquipmentPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ChangeNotifierProvider<EquipmentProvider>(
      create: (context) => EquipmentProvider(),
      child: Consumer<EquipmentProvider>(
        builder: (context, equipmentProvider, _) {
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(
              title: l10n.allEquipments,
              horizontalPadding: AppSize.w10,
              canPop: false,
              actions: [
                IconButton(
                  onPressed: () {
                    AppNavigationService.pushNamed(
                      context,
                      AppRoutes.equipmentAddUpdateScreen,
                      extra: EquipmentAddUpdateParams(),
                      afterBack: (value) {
                        if (value != null && value is bool) {
                          if (value) {
                            equipmentProvider.refreshEquipmentListData(
                              isWantShowLoader: true,
                            );
                          }
                        }
                      },
                    );
                  },
                  icon: Icon(
                    Icons.add,
                    color: AppColors.primaryColor,
                    size: AppSize.sp28,
                  ),
                ),
                Gap(AppSize.w10),
              ],
            ),
            body: ValueListenableBuilder<bool>(
              valueListenable: equipmentProvider.isShowLoader,
              builder: (context, loading, child) {
                return AppLoader(
                  isShowLoader: loading,
                  child: child!,
                );
              },
              child: AppPadding.symmetric(
                horizontal: AppSize.appPadding,
                child: EasyRefresh(
                  header: AppCommonFunctions.getLoadingHeader(),
                  footer: AppCommonFunctions.getLoadingFooter(),
                  controller: equipmentProvider.refreshController,

                  onRefresh: () => equipmentProvider.refreshEquipmentListData(),
                  // canLoadAfterNoMore: equipmentProvider.nextUrl.isNotEmptyAndNotNull,
                  onLoad: () => equipmentProvider.nextUrl.isEmptyOrNull
                      ? null
                      : equipmentProvider.getEquipmentDataApiCall(
                          isPagination: true,
                        ),
                  // .whenComplete(
                  //   () =>
                  //       equipmentProvider.refreshController.finishLoad(),
                  // ),
                  child: (equipmentProvider.equipmentListDataModel == null ||
                          (equipmentProvider.equipmentListDataModel?.results ??
                                  [])
                              .isEmpty)
                      ? ValueListenableBuilder(
                          valueListenable: equipmentProvider.isShowLoader,
                          builder: (context, loading, child) {
                            return loading
                                ? const SizedBox.expand()
                                : ListView(
                                  children: [
                                    SizedBox(
                                      height: MediaQuery.of(context).size.height * 0.8,
                                      child: Center(
                                          child: Text(
                                            l10n.noEquipmentsAvailable,
                                            style:
                                                context.theme.textTheme.displaySmall,
                                          ),
                                        ),
                                    ),
                                  ],
                                );
                          },
                        )
                      : ListView.builder(
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: EdgeInsets.only(bottom: AppSize.h16),
                          itemCount: equipmentProvider
                              .equipmentListDataModel!.results!.length,
                          itemBuilder: (context, index) {
                            final equipmentData = equipmentProvider
                                .equipmentListDataModel?.results?[index];
                            return AppPadding(
                              bottom: AppSize.h16,
                              child: Column(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(AppSize.h16),
                                    decoration: BoxDecoration(
                                      color: AppColors.white,
                                      borderRadius:
                                          BorderRadius.circular(AppSize.r4),
                                    ),
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              padding: EdgeInsets.all(
                                                AppSize.h8,
                                              ),
                                              decoration: BoxDecoration(
                                                color: AppColors.ffF8F9FA,
                                                borderRadius:
                                                    BorderRadius.circular(
                                                  AppSize.r4,
                                                ),
                                              ),
                                              child: SizedBox(
                                                height: AppSize.h32,
                                                width: AppSize.h32,
                                                child: equipmentData
                                                            ?.equipmentType ==
                                                        EquipmentType
                                                            .CAR_HAULER.name
                                                    ? AppAssets
                                                        .iconsCarHaulerTruck
                                                        .image()
                                                    : equipmentData
                                                                ?.equipmentType ==
                                                            EquipmentType.FLAT_BED
                                                                .name
                                                        ? AppAssets
                                                            .iconsFlatBedTruck
                                                            .image()
                                                        : AppAssets
                                                            .iconsTowTruck
                                                            .image(),
                                              ),
                                            ),
                                            Expanded(
                                              child: AppPadding.symmetric(
                                                horizontal: AppSize.w8,
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceEvenly,
                                                  children: [
                                                    Text(
                                                      l10n.equipmentName,
                                                      style: context
                                                          .textTheme.bodyMedium
                                                          ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: AppSize.sp12,
                                                        color:
                                                            AppColors.ff6C757D,
                                                      ),
                                                    ),
                                                    Gap(AppSize.h2),
                                                    Text(
                                                      equipmentData?.name ?? '',
                                                      maxLines: 2,
                                                      style: context
                                                          .textTheme.bodyMedium
                                                          ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize: AppSize.sp16,
                                                        color:
                                                            AppColors.ff343A40,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Row(
                                              children: [
                                                GestureDetector(
                                                  onTap: () async {
                                                    await AppNavigationService
                                                        .pushNamed(
                                                      context,
                                                      AppRoutes
                                                          .equipmentAddUpdateScreen,
                                                      extra:
                                                          EquipmentAddUpdateParams(
                                                        equipmentData:
                                                            equipmentData,
                                                      ),
                                                      afterBack: (value) {
                                                        if (value != null &&
                                                            value is bool) {
                                                          if (value) {
                                                            equipmentProvider
                                                                .refreshEquipmentListData(
                                                              isWantShowLoader:
                                                                  true,
                                                            );
                                                          }
                                                        }
                                                      },
                                                    );
                                                  },
                                                  child: SizedBox(
                                                    height: AppSize.h20,
                                                    child: const Icon(
                                                      Icons.edit_rounded,
                                                      color: AppColors
                                                          .primaryColor,
                                                    ),
                                                  ),
                                                ),
                                                Gap(AppSize.w10),
                                                GestureDetector(
                                                  onTap: () async {
                                                    await context
                                                        .showAlertDialog(
                                                      title:
                                                          l10n.deleteEquipment,
                                                      content: l10n
                                                          .deleteEquipmentConfirmation,
                                                      defaultActionText:
                                                          l10n.yes,
                                                      cancelActionText:
                                                          l10n.cancel,
                                                      cancelActionBgColor:
                                                          AppColors.white,
                                                      onCancelActionPressed:
                                                          Navigator.pop,
                                                      onDefaultActionPressed:
                                                          (dialogContext) {
                                                        Navigator.pop(
                                                          dialogContext,
                                                        );
                                                        if (equipmentData ==
                                                            null) {
                                                          return;
                                                        }
                                                        equipmentProvider
                                                            .deleteEquipmentApiCall(
                                                          context: context,
                                                          id: equipmentData
                                                                  .id ??
                                                              -1,
                                                          index: index,
                                                        );
                                                      },
                                                    );
                                                  },
                                                  child: SizedBox(
                                                    height: AppSize.h20,
                                                    child: AppAssets.iconsDelete
                                                        .image(),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        Gap(AppSize.h16),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              flex: 2,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceEvenly,
                                                children: [
                                                  Text(
                                                    l10n.type,
                                                    style: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: AppSize.sp12,
                                                      color: AppColors.ff6C757D,
                                                    ),
                                                  ),
                                                  Gap(AppSize.h2),
                                                  Text(
                                                    equipmentData?.equipmentType
                                                            ?.replaceAll(
                                                          '_',
                                                          ' ',
                                                        ) ??
                                                        '',
                                                    maxLines: 2,
                                                    style: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      fontSize: AppSize.sp16,
                                                      color: AppColors.ff343A40,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Expanded(
                                              flex: 2,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceEvenly,
                                                children: [
                                                  Text(
                                                    l10n.brand,
                                                    style: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: AppSize.sp12,
                                                      color: AppColors.ff6C757D,
                                                    ),
                                                  ),
                                                  Gap(AppSize.h2),
                                                  Text(
                                                    equipmentData?.brand ?? '',
                                                    maxLines: 2,
                                                    style: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      fontSize: AppSize.sp16,
                                                      color: AppColors.ff343A40,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceEvenly,
                                                children: [
                                                  Text(
                                                    l10n.year,
                                                    style: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: AppSize.sp12,
                                                      color: AppColors.ff6C757D,
                                                    ),
                                                  ),
                                                  Gap(AppSize.h2),
                                                  Text(
                                                    equipmentData?.year ?? '',
                                                    maxLines: 2,
                                                    style: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      fontSize: AppSize.sp16,
                                                      color: AppColors.ff343A40,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
