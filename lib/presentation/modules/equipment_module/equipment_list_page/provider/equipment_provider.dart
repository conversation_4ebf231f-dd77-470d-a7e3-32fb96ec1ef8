// ignore_for_file: public_member_api_docs

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';

import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/models/equipments_list_model.dart';
import 'package:transportmatch_provider/shared/repositories/equipments_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// Equipment Provider
class EquipmentProvider extends ChangeNotifier {
  EquipmentProvider() {
    getEquipmentDataApiCall(isWantShowLoader: true);
  }
  final refreshController = EasyRefreshController();

  bool isClosed = false;
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? getEquipmentCancelToken;
  CancelToken? deleteEquipmentCancelToken;

  EquipmentListDataModel? _equipmentListDataModel;
  EquipmentListDataModel? get equipmentListDataModel => _equipmentListDataModel;

  // Add a getter for next URL to make it easier to check in the UI
  String? get nextUrl => equipmentNextUrl;

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  Future<void> deleteEquipmentApiCall({
    required BuildContext context,
    required int id,
    required int index,
  }) async {
    if (isClosed) return;
    try {
      isShowLoader.value = true;
      deleteEquipmentCancelToken?.cancel();
      deleteEquipmentCancelToken = CancelToken();
      final request = ApiRequest(
        path: '${EndPoints.deleteEquipment}/$id/delete/',
        cancelToken: deleteEquipmentCancelToken,
      );
      final res = await Injector.instance<EquipmentsRepository>()
          .deleteEquipment(request);
      await res.when(
        success: (data) async {
          if (isClosed) return;
          isShowLoader.value = false;
          _equipmentListDataModel?.results?.removeAt(index);
          notify();
          context.l10n.equipmentDeletedSuccess.showSuccessAlert();
        },
        error: (exception) async {
          if (isClosed) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed) return;
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  Future<void> refreshEquipmentListData({
    bool isWantShowLoader = false,
  }) async {
    if (isClosed) return;
    _equipmentListDataModel = null;
    await getEquipmentDataApiCall(isWantShowLoader: isWantShowLoader);
    // refreshController.finishRefresh();
  }

  String? equipmentNextUrl;
  Future<void> getEquipmentDataApiCall({
    bool isWantShowLoader = false,
    bool isPagination = false,
  }) async {
    if (isClosed) return;
    try {
      getEquipmentCancelToken?.cancel();
      getEquipmentCancelToken = CancelToken();
      if (isWantShowLoader) {
        isShowLoader.value = true;
      }
      final request = ApiRequest(
        path: isPagination
            ? equipmentNextUrl ?? EndPoints.getEquipments
            : EndPoints.getEquipments,
        cancelToken: getEquipmentCancelToken,
      );

      // Injector.instance<AppDB>().token.logE;
      final res = await Injector.instance<EquipmentsRepository>()
          .getEquipments(request);

      if (isClosed) return;
      await res.when(
        success: (data) async {
          isShowLoader.value = false;
          if (isClosed && (getEquipmentCancelToken?.isCancelled ?? true)) {
            return;
          }
          equipmentNextUrl = data.next;
          if (_equipmentListDataModel != null && isPagination) {
            _equipmentListDataModel!.previous = data.previous;
            _equipmentListDataModel!.next = data.next;
            _equipmentListDataModel!.count = data.count;
            // ignore: unnecessary_cast
            for (final element
                // ignore: unnecessary_cast
                in (data.results ?? []) as List<EquipmentDataModel>) {
              if (!(_equipmentListDataModel?.results ?? [])
                  .any((val) => val.id == element.id)) {
                _equipmentListDataModel?.results?.add(element);
              }
            }
          } else {
            _equipmentListDataModel = data;
          }
          notify();
        },
        error: (exception) async {
          if (isClosed) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed) return;
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    getEquipmentCancelToken?.cancel();
    deleteEquipmentCancelToken?.cancel();
    refreshController.dispose();
    isShowLoader.dispose();
    _equipmentListDataModel = null;
    super.dispose();
  }
}
