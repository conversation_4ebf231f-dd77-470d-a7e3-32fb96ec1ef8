import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/widgets/vehicle_info_widgets.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

/// checkList VehiclesInfo widgets
class ChecklistVehiclesInfo extends StatelessWidget {
  /// Constructor
  const ChecklistVehiclesInfo({
    super.key,
    this.carBrand,
    this.carModel,
    this.carSerial,
    this.carYear,
    this.carSize,
    this.carCondition,
  });

  /// Car brand
  final String? carBrand;

  /// Car model
  final String? carModel;

  /// Car serial number
  final String? carSerial;

  /// Car year
  final String? carYear;

  /// Car carType
  final String? carSize;

  /// Car Description
  final String? carCondition;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSize.h12),
      margin: EdgeInsets.symmetric(horizontal: AppSize.w4),
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(color: AppColors.ffADB5BD),
        borderRadius: BorderRadius.circular(AppSize.r5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: AppSize.h8,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (carBrand.isNotEmptyAndNotNull)
                Flexible(
                  child: VehiclesInfoField(
                    title: context.l10n.carBrand,
                    value: carBrand!,
                  ),
                ),
              if (carSize.isNotEmptyAndNotNull)
                Flexible(
                  child: VehiclesInfoField(
                    title: context.l10n.carSize,
                    value: carSize!,
                  ),
                ),
              // const SizedBox.shrink(),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (carModel.isNotEmptyAndNotNull)
                Flexible(
                  child: VehiclesInfoField(
                    title: context.l10n.carModel,
                    value: carModel! + carModel! + carModel! + carModel!,
                  ),
                ),
              if (carYear.isNotEmptyAndNotNull)
                Flexible(
                  child: VehiclesInfoField(
                    title: context.l10n.carYear,
                    value: carYear!,
                  ),
                ),
            ],
          ),
          if (carSerial.isNotEmptyAndNotNull)
            VehiclesInfoField(
              title: context.l10n.carSerial,
              value: carSerial!,
            ),
          if (carCondition.isNotEmptyAndNotNull)
            VehiclesInfoField(
              title: context.l10n.carCondition,
              value: carCondition!,
            ),
        ],
      ),
    );
  }
}
