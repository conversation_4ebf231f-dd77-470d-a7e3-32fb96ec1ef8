import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/report_problem_page/models/report_problem_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/report_problem_page/provider/report_trip_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// Report Problem Screen
class ReportProblemPage extends StatelessWidget {
  const ReportProblemPage({super.key, required this.reportProblemParams});
  final ReportProblemParams reportProblemParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ReportTripProvider(),
      builder: (context, child) {
        final reportTripProvider = context.read<ReportTripProvider>();
        return Scaffold(
          backgroundColor: AppColors.ffF8F9FA,
          appBar: CustomAppBar(
            title: context.l10n.reportProblem,
          ),
          body: ValueListenableBuilder(
            valueListenable: reportTripProvider.isLoading,
            builder: (context, isLoading, child) {
              return AppLoader(
                isShowLoader: isLoading,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSize.w20,
                    vertical: AppSize.h10,
                  ),
                  child: Form(
                    key: reportTripProvider.formKey,
                    child: ListView(
                      primary: false,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: AppSize.h14,
                          children: [
                            AppTextFormField(
                              title: context.l10n.explainProblem,
                              textAction:TextInputAction.next,
                              keyboardType: TextInputType.multiline,
                              controller: reportTripProvider.explainProblem,
                              validator: (value) {
                                if (value!.isEmpty) {
                                  return context.l10n.pleaseEnterDescription;
                                }
                                return null;
                              },
                              maxLine: 5,
                              hintText: context.l10n.explainInBrief,
                            ),
                            AppTextFormField(
                              title:
                                  '${context.l10n.enterAffectedTime} (${context.l10n.inHours})',
                              hintText: context.l10n.inHours,
                              controller: reportTripProvider.affectedTime,
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value!.isEmpty) {
                                  return context.l10n.pleaseEnterAffectedTime;
                                }
                                return null;
                              },
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                FilteringTextInputFormatter.deny(RegExp('^0')),
                              ],
                              maxTextLength: 3,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          bottomNavigationBar: Padding(
            padding: EdgeInsets.symmetric(
              vertical: AppSize.h25,
              horizontal: AppSize.appPadding,
            ),
            child: AppButton(
              onPressed: () {
                if (reportTripProvider.formKey.currentState?.validate() ?? false) {
                  reportTripProvider.reportTripAPICall(
                    reportProblemParams.tripId.toString(),
                    context,
                  );
                }
              },
              text: context.l10n.submitReport,
            ),
          ),
        );
      },
    );
  }
}
