
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/widgets/ongoing_transporter_card.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/provider/accepted_trips_provider.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';

///Ongoing Screen
class OngoingPage extends StatelessWidget {
  /// Constructor
  const OngoingPage({super.key, required this.acceptedTripsProvider});
  final AcceptedTripsProvider acceptedTripsProvider;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: acceptedTripsProvider
        ..getAcceptedTrips(
          status: AcceptedTripType.ONGOING.name,
          isWantShowLoader: true,
        ),
      child: ValueListenableBuilder(
        valueListenable: acceptedTripsProvider.isShowLoaderForOngoingTrips,
        builder: (context, isShowLoader, child) {
          return AppLoader(
            isShowLoader: isShowLoader,
            child: Selector<AcceptedTripsProvider,
                (List<AcceptedTripModelData>, String?)>(
              selector: (context, provider) =>
                  (provider.ongoingTripsList, provider.ongoingTripsNextUrl),
              builder: (context, value, child) {
                return EasyRefresh(
                  header: AppCommonFunctions.getLoadingHeader(),
                  footer: AppCommonFunctions.getLoadingFooter(),
                  controller:
                      acceptedTripsProvider.ongoingPageRefreshController,
                  onRefresh: () => acceptedTripsProvider.getAcceptedTrips(
                    status: AcceptedTripType.ONGOING.name,
                  ),
                  onLoad: value.$2.isNotEmptyAndNotNull
                      ? () => acceptedTripsProvider.getAcceptedTrips(
                            status: AcceptedTripType.ONGOING.name,
                            isPagination: true,
                          )
                      : null,
                  child: value.$1.isEmpty && !isShowLoader
                      ? ListView(
                        children: [
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.75,
                            child: Center(
                                child: Text(context.l10n.noTripStartedYet),
                              ),
                          ),
                        ],
                      )
                      : ListView.builder(
                          itemCount: value.$1.length,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding:
                                  EdgeInsets.symmetric(vertical: AppSize.h5),
                              child: OngoingTransporterCard(
                                data: value.$1[index],
                              ),
                            );
                          },
                        ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
