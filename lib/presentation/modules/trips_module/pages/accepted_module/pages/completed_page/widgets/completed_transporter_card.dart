import 'package:flutter/material.dart';
import 'package:flutter_rating/flutter_rating.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

///Completed Transporter Card
class CompletedTransporterCard extends StatelessWidget {
  /// Constructor
  const CompletedTransporterCard({
    super.key,
    required this.data,
  });

  final AcceptedTripModelData data;

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.white,
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: AppSize.w10,
          vertical: AppSize.h10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with "Your Shipment" and "Edit"
            Padding(
              padding: EdgeInsets.only(bottom: AppSize.h2),
              child: Text(
                context.l10n.no_of_slots,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: AppColors.ff6C757D,
                  fontSize: AppSize.sp12,
                ),
              ),
            ),
            Text(
              data.totalBookedSlots?.toString() ?? '0',
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: AppSize.sp18,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.h10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: AppSize.w4,
                children: [
                  AppAssets.iconsLocationOrigin.image(
                    height: AppSize.h14,
                  ),
                  const Expanded(child: Divider()),
                  AppAssets.iconsLocation.image(
                    height: AppSize.h14,
                  ),
                ],
              ),
            ),
            TitleInfoWidget(
              title: context.l10n.deliveredTo,
              subTitle: data.endStopLocation?.address?.street ??
                  data.exclusiveTrips?.userStartLocation?.street ??
                  '',
              titleColor: AppColors.ff6C757D,
              titleFontSize: AppSize.sp12,
              titleFontWeight: FontWeight.w500,
              subTitleFontSize: AppSize.sp14,
              subTitleFontWeight: FontWeight.w400,
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.h10),
              child: Text.rich(
                TextSpan(
                  text: '${context.l10n.youHaveDeliveredThisShipmentOn} ',
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: AppColors.ff6C757D,
                    fontSize: AppSize.sp12,
                  ),
                  children: [
                    TextSpan(
                      text: data.completedAt?.monthDate ?? '',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: AppColors.ff6C757D,
                        fontSize: AppSize.sp12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (data.ratings != null && data.ratings?.toInt() != 0)
              Row(
                children: [
                  StarRating(
                    rating: data.ratings?.toDouble() ?? 0,
                    allowHalfRating: true,
                    color: Colors.amber,
                    emptyIcon: Icons.star_outline_rounded,
                    filledIcon: Icons.star_rounded,
                    size: AppSize.sp30,
                  ),
                  Text(
                    data.ratings?.toStringAsFixed(1) ?? '0',
                    style: context.textTheme.titleLarge?.copyWith(
                      color: AppColors.ff6C757D,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
