
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/widgets/upcoming_card_widgets.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/provider/accepted_trips_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';

/// Upcoming Screen
class UpcomingPage extends StatefulWidget {
  /// Constructor
  const UpcomingPage({super.key, required this.acceptedTripsProvider});
  final AcceptedTripsProvider acceptedTripsProvider;

  @override
  State<UpcomingPage> createState() => _UpcomingPageState();
}

class _UpcomingPageState extends State<UpcomingPage> {
  @override
  void initState() {
    widget.acceptedTripsProvider.getAcceptedTrips(isWantShowLoader: true);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.acceptedTripsProvider.isShowLoaderForActiveTrips,
      builder: (context, isShowLoader, child) {
        return AppLoader(
          isShowLoader: isShowLoader,
          child: Selector<AcceptedTripsProvider,
              (List<AcceptedTripModelData>, String?)>(
            selector: (context, provider) =>
                (provider.activeTripsList, provider.activeTripsNextUrl),
            builder: (context, value, child) {
              return EasyRefresh(
                header: AppCommonFunctions.getLoadingHeader(),
                footer: AppCommonFunctions.getLoadingFooter(),
                controller:
                    widget.acceptedTripsProvider.activePageRefreshController,
                onRefresh: () =>
                    widget.acceptedTripsProvider.getAcceptedTrips(),
                onLoad: value.$2.isNotEmptyAndNotNull
                    ? () => widget.acceptedTripsProvider
                        .getAcceptedTrips(isPagination: true)
                    : null,
                child: value.$1.isEmpty && !isShowLoader
                    ? ListView(
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.75,
                          child: Center(
                              child: Text(
                                context.l10n.noUpcomingTripAvailableYet,
                                style: context.textTheme.bodyMedium,
                              ),
                            ),
                        ),
                      ],
                    )
                    : ListView.builder(
                        itemCount: value.$1.length,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          final tripModel = value.$1[index];
                          return Padding(
                            padding: EdgeInsets.only(bottom: AppSize.h16),
                            child: UpcomingCardWidgets(
                              onDetailsPressed: () =>
                                  AppNavigationService.pushNamed(
                                context,
                                AppRoutes.tripsAllShipmentsScreen,
                                extra: AllShipmentsParams(
                                  tripId: tripModel.id,
                                  slots: tripModel.totalBookedSlots,
                                ),
                              ),
                              tripModel: tripModel,
                            ),
                          );
                        },
                      ),
              );
            },
          ),
        );
      },
    );
  }
}
