// ignore_for_file: public_member_api_docs

import 'dart:convert';
import 'dart:developer';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_keys.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class TripDetailsProvider extends ChangeNotifier {
  TripDetailsProvider(this.tripId) {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) async => getData(tripId),
    );
  }

  Future<void> getData(int? tripId) async {
    if (isClosed || tripId == null) return;
    await Future.wait([
      getAcceptedTripDetail(tripId),
      getTripBookings(tripId),
    ]);
    isShowLoading.value = false;
  }

  final refreshController = EasyRefreshController();

  List<BookingModel> bookingList = [];

  final int? tripId;
  ValueNotifier<bool> isShowLoading = ValueNotifier(true);
  bool isClosed = false;

  /// enable edit Details
  bool isEdit = false;

  /// for show selected tab
  int selectedValue = 0;

  /// for tab selected
  int selectedIndex = 0;

  TripDetailsModel? tripDetailsModel;

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// update is edit
  void editUpdate({required bool value}) {
    if (isClosed) return;
    try {
      isEdit = value;
      notify();
    } catch (e) {
      '======>>>> editUpdate error $e'.logE;
    }
  }

  /// update is edit
  void selectedIndexUpdate({required int value}) {
    if (isClosed) return;
    try {
      selectedIndex = value;
      notify();
    } catch (e) {
      '======>>>> selectedIndexUpdate error $e'.logE;
    }
  }

  /// formate the affected time
  String formateAffectedTime(String? affectedTime, BuildContext context) {
    final parts = affectedTime?.split(' ');

    if (parts != null && parts.isNotEmpty && parts.length == 2) {
      final day = parts[0];
      final hour = parts[1].split(':')[0];
      return hour == '00'
          ? '$day ${context.l10n.day}'
          : '$day ${context.l10n.day} $hour ${context.l10n.hour}';
    } else if (parts != null && parts.isNotEmpty && parts.length == 1) {
      final hour = parts[0].split(':')[0];
      return hour == '00' ? '' : '$hour ${context.l10n.hour}';
    }
    return '';
  }

  /// get accepted trip detail api
  CancelToken? cancelToken;
  Future<void> getAcceptedTripDetail(int bookingId) async {
    if (isClosed) return;
    cancelToken?.cancel();
    cancelToken = CancelToken();
    // isLoading = true;
    try {
      final result =
          await Injector.instance<TripRepository>().getAcceptedTripDetail(
        ApiRequest(
          path: EndPoints.getAcceptedTripDetail(bookingId.toString()),
          cancelToken: cancelToken,
        ),
      );

      await result.when(
        success: (data) async {
          log('======>>>> getAcceptedTripDetail ${jsonEncode(data)}');
          if (isClosed || (cancelToken?.isCancelled ?? true)) return;
          tripDetailsModel = data;
          notify();
        },
        error: (exception) async {
          isShowLoading.value = false;
          if (isClosed || (cancelToken?.isCancelled ?? true)) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      isShowLoading.value = false;
      if (isClosed || (cancelToken?.isCancelled ?? true)) return;
      '======>>>> getAcceptedTripDetail error $e'.logE;
    }
    // isLoading = false;
  }

  /// get accepted trip detail api
  CancelToken? bookingCancelToken;
  Future<void> getTripBookings(int bookingId) async {
    if (isClosed) return;
    bookingCancelToken?.cancel();
    bookingCancelToken = CancelToken();
    // isLoading = true;
    try {
      final result = await Injector.instance<TripRepository>()
          .getAcceptedTripBookingDetail(
        ApiRequest(
          path: EndPoints.getAcceptedTripBookings(bookingId.toString()),
          cancelToken: bookingCancelToken,
        ),
      );

      await result.when(
        success: (data) async {
          if (isClosed || (bookingCancelToken?.isCancelled ?? true)) return;
          bookingList = data;
          notify();
        },
        error: (exception) async {
          isShowLoading.value = false;
          if (isClosed || (bookingCancelToken?.isCancelled ?? true)) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      isShowLoading.value = false;
      if (isClosed || (bookingCancelToken?.isCancelled ?? true)) return;
      '======>>>> getTripBookings error $e'.logE;
    }
    // isLoading = false;
  }

  /// get accepted trip detail api
  CancelToken? startCancelToken;
  Future<void> startCompleteTrip(
    int? tripId, {
    bool isComplete = false,
    required BuildContext context,
  }) async {
    if (isClosed || tripId == null) return;
    startCancelToken?.cancel();
    startCancelToken = CancelToken();
    isShowLoading.value = true;
    try {
      final result =
          await Injector.instance<TripRepository>().startOngoingOutDeliveryTrip(
        ApiRequest(
          path: isComplete
              ? EndPoints.completeTrip(tripId.toString())
              : EndPoints.startTrip(tripId.toString()),
          cancelToken: startCancelToken,
        ),
      );

      await result.when(
        success: (data) async {
          log('======>>>> start complete Trip ${jsonEncode(data)}');
          if (isClosed || (startCancelToken?.isCancelled ?? true)) return;
          AppNavigationService.pop(context);
          'Trip ${isComplete ? 'completed' : 'started'} successfully'
              .showSuccessAlert();
          notify();
        },
        error: (exception) async {
          if (isClosed || (startCancelToken?.isCancelled ?? true)) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      isShowLoading.value = false;
      if (isClosed || (startCancelToken?.isCancelled ?? true)) return;
      '======>>>> startTrip error $e'.logE;
    }
    isShowLoading.value = false;
  }

  Future<void> outForDelivery(
    int? tripId, {
    required BuildContext context,
  }) async {
    if (isClosed || tripId == null) return;
    startCancelToken?.cancel();
    startCancelToken = CancelToken();
    isShowLoading.value = true;
    try {
      final result =
          await Injector.instance<TripRepository>().startOngoingOutDeliveryTrip(
        ApiRequest(
          path: EndPoints.outForDelivery(tripId.toString()),
          cancelToken: startCancelToken,
        ),
      );

      await result.when(
        success: (data) async {
          if (isClosed || (startCancelToken?.isCancelled ?? true)) return;
          context.pop();
          notify();
        },
        error: (exception) async {
          if (isClosed || (startCancelToken?.isCancelled ?? true)) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      isShowLoading.value = false;
      if (isClosed || (startCancelToken?.isCancelled ?? true)) return;
      '======>>>> out for delivery error $e'.logE;
    }
    isShowLoading.value = false;
  }

  Future<void> readyToCollect(
    int? tripId, {
    required BuildContext context,
  }) async {
    if (isClosed || tripId == null) return;
    startCancelToken?.cancel();
    startCancelToken = CancelToken();
    isShowLoading.value = true;
    try {
      final result =
          await Injector.instance<TripRepository>().startOngoingOutDeliveryTrip(
        ApiRequest(
          path: EndPoints.readyToCollect(tripId.toString()),
          cancelToken: startCancelToken,
        ),
      );

      await result.when(
        success: (data) async {
          if (isClosed || (startCancelToken?.isCancelled ?? true)) return;
          context.pop();
          notify();
        },
        error: (exception) async {
          if (isClosed || (startCancelToken?.isCancelled ?? true)) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      isShowLoading.value = false;
      if (isClosed || (startCancelToken?.isCancelled ?? true)) return;
      '======>>>> ready for delivery error $e'.logE;
    }
    isShowLoading.value = false;
  }

  CancelToken? tripCancelToken;
  Future<void> getCancelTrip(int tripId, BuildContext context) async {
    if (isClosed) return;

    tripCancelToken?.cancel();
    tripCancelToken = CancelToken();
    isShowLoading.value = true;

    try {
      final result = await Injector.instance<TripRepository>().cancelAllTrips(
        ApiRequest(
          path: EndPoints.cancelTrip,
          cancelToken: tripCancelToken,
          data: {
            ApiKeys.trip: tripId,
            ApiKeys.cancelReason: 'TRIP_CANCELLED_BY_PROVIDER',
          },
        ),
      );

      await result.when(
          success: (data) async {
            if (isClosed || (tripCancelToken?.isCancelled ?? true)) return;

            'Trip deleted successfully'.showSuccessAlert();
            // if (!context.mounted) return;
            AppNavigationService.pop(context);
            isShowLoading.value = false;

            notify();
          },
          error: (exception) async {
            isShowLoading.value = false;
            if (isClosed || (tripCancelToken?.isCancelled ?? true)) return;
            exception.message.showErrorAlert();
        },
      );
      notify();
    } catch (e) {
      isShowLoading.value = false;
      if (isClosed || (tripCancelToken?.isCancelled ?? true)) return;
      notify();
      '======>>>> getCancelTrip error $e'.logE;
    }
  }





  @override
  void dispose() {
    isClosed = true;
    cancelToken?.cancel();
    bookingCancelToken?.cancel();
    startCancelToken?.cancel();
    tripCancelToken?.cancel();
    isShowLoading.dispose();
    bookingList.clear();
    super.dispose();
  }
}
