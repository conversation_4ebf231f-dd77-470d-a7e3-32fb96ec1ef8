import 'package:custom_info_window/custom_info_window.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/places_api_provider_class.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/pages/search_address_page/widgets/triangle_painter.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/route_pages/model/router_request_param.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/logger.dart';
import 'package:transportmatch_provider/widgets/marqee_widget.dart';

class RouteProvider extends ChangeNotifier {
  RouteProvider(this.routerRequestParam,) {
    Future.delayed(Durations.long4,()async{
      await addMarker(routerRequestParam.addressList ?? [],rootNavKey.currentContext!);
    });
  }
  final RouterRequestParam routerRequestParam;

  /// when provider is closed
  bool _isClosed = false;

  /// Controller for custom info window
  final customInfoWindowController = CustomInfoWindowController();

  /// Loading state flag
  final isShowLoader = ValueNotifier(false);

  /// redirecting to google map with routes url
  final redirectingUrl = ValueNotifier('');

  /// Set of markers on the map
  final Set<Marker> markers = {};

  List<Polyline> polyline = <Polyline>[];

  /// Camera position of the map
  final initialCameraPosition = const CameraPosition(
    target: LatLng(37.42796133580664, -122.085749655962),
    zoom: 14.4746,
  );

  /// Flag to check if map is created
  bool isMapCreated = false;

  /// Called when map is created
  /// [controller] is the GoogleMapController instance
  void onMapCreated(GoogleMapController controller) {
    if (_isClosed) return;

    if (!isMapCreated) {
      customInfoWindowController.googleMapController = controller;
      isMapCreated = true;
      notify();
    }
  }

  /// on google map camera move callback
  /// to update the custom info window position
  void onCameraMove(CameraPosition position) {
    if (_isClosed) return;

    customInfoWindowController.onCameraMove?.call();
    notify();
  }

  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// Add stop location markers on the map
  /// [list] is the list of stop locations
  /// [context] is the BuildContext
  Future<void> addMarker(
    List<StopLocation?> list,
    BuildContext context,
  ) async {
    if (_isClosed) return;
    isShowLoader.value = true;

    try {
      markers.clear();
      if (_isClosed) return;
      for (final element in list) {
        if (_isClosed) break;

        final lat = double.tryParse(element?.address?.latitude ?? '');
        final long = double.tryParse(element?.address?.longitude ?? '');
        final isIntermediate = element?.id != null;
        if (lat != null && long != null) {
          final latLng = LatLng(lat, long);
          markers.add(
            Marker(
              markerId: MarkerId(
                '${element?.address?.latitude},${element?.address?.longitude}',
              ),
              position: latLng,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                isIntermediate
                    ? BitmapDescriptor.hueRed
                    : BitmapDescriptor.hueViolet,
              ),
              onTap: () {
                if (isIntermediate) {
                  customInfoWindowController.addInfoWindow!(
                    Column(
                      children: [
                        Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(AppSize.r10),
                            ),
                            width: double.infinity,
                            height: double.infinity,
                            padding: EdgeInsets.all(AppSize.sp10),
                            child: MarqueeWidget(
                              child: Text(
                                element?.name ?? '',
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                style: context.textTheme.bodyMedium?.copyWith(
                                  color: AppColors.white,
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                        CustomPaint(
                          size: const Size(20, 10),
                          painter: RPSCustomPainter(),
                        ),
                      ],
                    ),
                    latLng,
                  );
                }
              },
            ),
          );
        }
      }
      final bounds = PlaceApiProvider().getLatLngBounds(
        list
            .map(
              (e) => LatLng(
                double.parse(e?.address?.latitude ?? '0'),
                double.parse(e?.address?.longitude ?? '0'),
              ),
            )
            .toList(),
      );

      /// we need future delayed to wait for map to be created
      await Future.delayed(Durations.long4);
      await customInfoWindowController.googleMapController?.animateCamera(
        CameraUpdate.newLatLngBounds(bounds, 50),
      );

      await PlaceApiProvider()
          .getRouteWithWaypoints(
        destination: LatLng(
          double.parse(
            routerRequestParam.destinationLocation?.address?.latitude ?? '0.0',
          ),
          double.parse(
            routerRequestParam.destinationLocation?.address?.longitude ?? '0.0',
          ),
        ),
        origin: LatLng(
          double.parse(list.firstOrNull?.address?.latitude ?? '0'),
          double.parse(list.firstOrNull?.address?.longitude ?? '0'),
        ),
        waypoints: list
            .map(
              (e) => LatLng(
                double.parse(e?.address?.latitude ?? '0'),
                double.parse(e?.address?.longitude ?? '0'),
              ),
            )
            .toList(),
      )
          .then(
        (result) {
          final waypointsBuffer = StringBuffer();
          if (result.optimizedWaypoints.isNotEmpty) {
            waypointsBuffer.write('&waypoints=');
            for (final wp in result.optimizedWaypoints) {
              if (result.optimizedWaypoints.lastOrNull != wp &&
                  result.optimizedWaypoints.firstOrNull != wp) {
                waypointsBuffer.write('|${wp.latitude},${wp.longitude}');
              }
            }
          }
          redirectingUrl.value =
              'https://www.google.com/maps/dir/?api=1&${result.url}$waypointsBuffer';
          return polyline = [
            Polyline(
              polylineId: const PolylineId('polyline'),
              points: result.routeList,
              color: AppColors.ff0087C7,
            ),
          ];
        },
      );

      notify();
    } catch (e) {
      'addMarker error: $e'.logE;
    } finally {
      isShowLoader.value = false;
    }
  }

  @override
  void dispose() {
    _isClosed = false;
    super.dispose();
  }
}
