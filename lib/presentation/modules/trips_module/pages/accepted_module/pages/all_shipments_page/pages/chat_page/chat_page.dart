
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/models/chat_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/provider/chat_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/widgets/chat_bubble.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// Chat Screen UI
class ChatPage extends StatefulWidget {
  /// Constructor
  const ChatPage({super.key, required this.chatParams});

  final ChatParams chatParams;

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((t) {
      WidgetsBinding.instance.addObserver(this);
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    context.read<ChatProvider>().handleAppLifecycleState(state);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.addPostFrameCallback((t) {
      WidgetsBinding.instance.removeObserver(this);
    });
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.pageBGColor,
      appBar: CustomAppBar(
        title: widget.chatParams.title,
      ),
      body: AppPadding.symmetric(
        horizontal: AppSize.appPadding,
        child: Consumer<ChatProvider>(
          builder: (context, chatProvider, _) {
            final groupedMessages =
                chatProvider.groupedMessages.entries.toList();
            return AppLoader(
              isShowLoader: chatProvider.isLoading,
              child: Column(
                children: [
                  Expanded(
                    child: chatProvider.messages.isEmpty
                        ? !chatProvider.isLoading
                            ? Center(
                                child: Text(
                                  l10n.noMessagesYet,
                                  style: context.textTheme.bodyLarge!.copyWith(
                                    color: AppColors.ff6C757D,
                                  ),
                                ),
                              )
                            : const SizedBox.expand()
                        : EasyRefresh(
                            header: AppCommonFunctions.getLoadingHeader(),
                            footer: AppCommonFunctions.getLoadingFooter(),
                            controller: chatProvider.refreshController,
                            onLoad: chatProvider.nextUrl.isNotEmptyAndNotNull
                                ? () => chatProvider.getOldChatMessages(
                                      widget.chatParams
                                          .customerChatRoomParameter!.id
                                          .toString(),
                                    )
                                : null,
                            child: ListView.builder(
                              reverse: true,
                              padding: EdgeInsets.zero,
                              controller: chatProvider.scrollController,
                              itemCount: groupedMessages.length,
                              itemBuilder: (context, groupIndex) {
                                final group = groupedMessages[groupIndex];
                                final dateTitle = group.key;
                                final messages = group.value;

                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                        vertical: AppSize.h8,
                                      ),
                                      child: Center(
                                        child: Text(
                                          dateTitle,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                    ...List.generate(messages.length,
                                        (msgIndex) {
                                      final msg = messages[msgIndex];
                                      final isNextMsgSameSender = msgIndex + 1 <
                                              messages.length &&
                                          messages[msgIndex + 1].sender?.id ==
                                              msg.sender?.id;
                                      return ChatBubble(
                                        message: msg,
                                        isUser: msg.sender?.id ==
                                            chatProvider.userId,
                                        isNextMsgSameSender:
                                            isNextMsgSameSender,
                                      );
                                    }).reversed,
                                  ],
                                );
                              },
                            ),
                          ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                    child: ((widget.chatParams.customerChatRoomParameter
                                    ?.isActive ??
                                false) ||
                            (chatProvider.chatRoom?.isActive ?? false))
                        ? AppTextFormField(
                            controller: chatProvider.chatController,
                            hintText: context.l10n.writeUrMessageHere,
                            keyboardType: TextInputType.multiline,
                            maxTextLength: 300,
                            maxLine: 4,
                            suffixIcon: GestureDetector(
                              onTap: () {
                                if ((widget.chatParams.customerChatRoomParameter
                                            ?.isActive ??
                                        false) ||
                                    (chatProvider.chatRoom?.isActive ??
                                        false)) {
                                  chatProvider.sendMessage();
                                }
                              },
                              child: AppAssets.iconsSend.image(
                                height: AppSize.h20,
                                width: AppSize.h20,
                              ),
                            ),
                          )
                        : !chatProvider.isLoading
                            ? Text(
                                l10n.chatIsInactive,
                              )
                            : const SizedBox.expand(),
                  ),
                  Gap(AppSize.h10),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
