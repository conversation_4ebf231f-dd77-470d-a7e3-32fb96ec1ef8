import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

class StarAndStopLocationFullAddressWidget extends StatelessWidget {
  const StarAndStopLocationFullAddressWidget({
    super.key,
    // required this.acceptedTripModel,
    required this.booking,
    required this.tripDetailsModel,
    required this.isExclusive,
  });
  // final AcceptedTripModelData acceptedTripModel;
  final BookingModel? booking;
  final TripDetailsModel tripDetailsModel;
  final bool isExclusive;

  // /// Open chat with origin stock admin
  // void _openChatWithOriginStockAdmin(BuildContext context) {
  //   if (booking == null) return;
  //   final sharedBooking = booking!.sharedBookings?.firstOrNull;
  //   if (sharedBooking == null) return;

  //   final originStockLocation = sharedBooking.intermediateStartStopLocation;
  //   if (originStockLocation == null) return;

  //   final stockLocationId = originStockLocation.stopLocation?.id ?? 0;
  //   final bookingId = booking!.id ?? -1;

  //   if (stockLocationId <= 0 || bookingId <= 0) return;

  //   final locationName = originStockLocation.stopLocation?.name ?? '';

  //   AppNavigationService.pushNamed(
  //     context,
  //     ChatScreen(
  //       title: locationName.isEmpty ? l10n.originStockLocation : locationName,
  //       receiverId: stockLocationId.toInt(),
  //       bookingDetailId: bookingId,
  //       chatType: ChatType.originStock,
  //     ),
  //   );
  // }

  // /// Open chat with drop stock admin
  // void _openChatWithDropStockAdmin(BuildContext context) {
  //   if (booking == null) return;
  //   final sharedBooking = booking!.sharedBookings?.firstOrNull;
  //   if (sharedBooking == null) return;

  //   final dropStockLocation = sharedBooking.intermediateEndStopLocation;
  //   if (dropStockLocation == null) return;

  //   final stockLocationId = dropStockLocation.stopLocation?.id ?? 0;
  //   final bookingId = booking!.id ?? -1;

  //   if (stockLocationId <= 0 || bookingId <= 0) return;

  //   final locationName = dropStockLocation.stopLocation?.name ?? '';

  //   AppNavigationService.pushNamed(
  //     context,
  //     ChatScreen(
  //       title: locationName.isEmpty ? l10n.dropStockLocation : locationName,
  //       receiverId: stockLocationId.toInt(),
  //       bookingDetailId: bookingId,
  //       chatType: ChatType.dropStock,
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: AppSize.h16,
      children: [
        StarAndStopLocationFullAddressWidgetPart(
          title: context.l10n.originStockLocation,
          value: isExclusive ? tripDetailsModel.exclusiveTripData?.userStartLocation?.city ?? '-':
              tripDetailsModel.startStopLocation?.address?.fullAddress ?? '-',
          onTap: () {
            // _openChatWithOriginStockAdmin(context);
          },
        ),
        StarAndStopLocationFullAddressWidgetPart(
          title: context.l10n.dropStockLocation,
          value: isExclusive?tripDetailsModel.exclusiveTripData?.userEndLocation?.city ?? '-' : tripDetailsModel.endStopLocation?.address?.fullAddress ?? '-',
          onTap: () {
            // _openChatWithDropStockAdmin(context);
          },
        ),
      ],
    );
  }
}

class StarAndStopLocationFullAddressWidgetPart extends StatelessWidget {
  const StarAndStopLocationFullAddressWidgetPart({
    super.key,
    required this.title,
    required this.value,
    required this.onTap,
  });

  final String title;
  final String value;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(AppSize.r4),
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.w16,
        vertical: AppSize.h10,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '$title: ',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.ffADB5BD,
                ),
              ),
              // InkWell(
              //   onTap: onTap,
              //   child: AppAssets.iconsChat
              //       .image(height: AppSize.sp20, width: AppSize.sp20),
              // ),
            ],
          ),
          Gap(AppSize.h4),
          Text(
            value,
            style: context.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
        ],
      ),
    );
  }
}
