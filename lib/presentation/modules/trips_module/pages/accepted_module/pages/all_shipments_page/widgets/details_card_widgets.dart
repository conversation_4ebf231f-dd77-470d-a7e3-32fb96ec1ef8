import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/booking_detail_page/models/booking_detail_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/enum/chat_type.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/models/chat_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';
import 'package:url_launcher/url_launcher_string.dart';

/// DetailsCard ui
class DetailsCardWidgets extends StatefulWidget {
  /// Constructor
  const DetailsCardWidgets({
    super.key,
    required this.booking,
    // required this.acceptedTripModelData,
    required this.tripDetailsModel,
    required this.isExclusive,
    this.bookingId,
    required this.onInit,
    this.slots,
  });
  final BookingModel booking;
  // final AcceptedTripModelData acceptedTripModelData;
  final TripDetailsModel tripDetailsModel;
  final int? bookingId;
  final bool isExclusive;
  final num? slots;


  /// this function is called in init state
  /// for automatically scroll to the selected booking
  final Function(BuildContext context) onInit;

  @override
  State<DetailsCardWidgets> createState() => _DetailsCardWidgetsState();
}

class _DetailsCardWidgetsState extends State<DetailsCardWidgets> {
  bool isViewDetails = false;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        if (widget.bookingId == widget.booking.id) {
          widget.onInit(context);
        }
      },
    );
    super.initState();
  }

  void handleUpdateChatModel({
    int? chatRoomId,
    bool? isActive,
  }) {
    widget.booking.customerChatRoom = CustomerChatRoom(
      id: chatRoomId,
      isActive: isActive,
      unreadMessageCount: 0,
    );
    if (mounted) {
      setState(() {});
    }
  }

  /// Open chat with customer
  void _openChatWithCustomer(BuildContext context) {
    final customer = widget.booking.booking?.customer;
    if (customer == null) return;

    final customerId = widget.booking.booking?.customer?.user ?? -1;
    final bookingId = widget.booking.id ?? -1;
    final chatRoomData = widget.booking.customerChatRoom;

    if (customerId <= 0 || bookingId <= 0) return;

    final customerName =
        '${customer.firstName ?? ''} ${customer.lastName ?? ''}';
    handleUpdateChatModel(
      chatRoomId: chatRoomData?.id,
      isActive: chatRoomData?.isActive,
    );

    AppNavigationService.pushNamed(
      context,
      AppRoutes.tripsChatScreen,
      extra: ChatParams(
        receiverId: customerId,
        bookingDetailId: bookingId,
        chatType: ChatType.customer,
        customerChatRoomParameter: chatRoomData,
        updateChatModel: handleUpdateChatModel,
        title: customerName.trim().isEmpty ? 'Customer' : customerName,
      ),
      // {
      //   'receiverId': customerId,
      //   'bookingDetailId': bookingId,
      //   'chatType': ChatType.customer,
      //   'customerChatRoomParameter': chatRoomData,
      //   'updateChatModel': handleUpdateChatModel,
      //   'title': customerName.trim().isEmpty ? 'Customer' : customerName,
      // },

      // ChangeNotifierProvider(
      //   create: (context) => ChatProvider(
      //     receiverId: customerId,
      //     bookingDetailId: bookingId,
      //     chatType: ChatType.customer,
      //     customerChatRoomParameter: chatRoomData,
      //     updateChatModel: handleUpdateChatModel,
      //   ),
      //   child: ChatScreen(
      //     title: customerName.trim().isEmpty ? 'Customer' : customerName,
      //     customerChatRoomParameter: chatRoomData,
      //   ),
      // ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isConfirmed =
        widget.booking.status == BookingStatusType.CONFIRMED.name;

    final child = Column(
      children: [
        // Gap(AppSize.h16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                InkWell(
                  onTap: () => AppNavigationService.pushNamed(
                    context,
                    AppRoutes.tripsBookingDetailScreen,
                    extra: BookingDetailParams(
                      booking: widget.booking,
                      tripDetailsModel: widget.tripDetailsModel,
                    ),
                    // BookingDetail(
                    //   booking: widget.booking,
                    //   tripDetailsModel: widget.tripDetailsModel,
                    // ),
                  ),
                  child: Text(
                    context.l10n.details,
                    style: context.textTheme.titleLarge?.copyWith(
                      color: AppColors.primaryColor,
                      fontSize: AppSize.w16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Gap(AppSize.w12),
                if (Injector.instance<AppDB>()
                            .userModel
                            ?.user
                            ?.role
                            ?.toLowerCase() ==
                        UserType.Driver.name.toLowerCase() &&
                    widget.booking.status == BookingStatusType.ONGOING.name)
                  InkWell(
                    onTap: () => _openChatWithCustomer(context),
                    child: Badge.count(
                      isLabelVisible: widget.booking.customerChatRoom != null &&
                          (widget.booking.customerChatRoom!
                                      .unreadMessageCount ??
                                  0) >
                              0,
                      count: widget.booking.customerChatRoom != null &&
                              (widget.booking.customerChatRoom!
                                          .unreadMessageCount ??
                                      0) >
                                  0
                          ? widget.booking.customerChatRoom!
                                  .unreadMessageCount ??
                              0
                          : 0,
                      textColor: AppColors.white,
                      child: AppAssets.iconsChat
                          .image(height: AppSize.sp20, width: AppSize.sp20),
                    ),
                  ),
              ],
            ),
            if (!widget.isExclusive && isConfirmed)
              Text(
                context.l10n.cancelTrip,
                style: context.textTheme.titleLarge?.copyWith(
                  color: AppColors.red,
                  fontSize: AppSize.w16,
                  fontWeight: FontWeight.w600,
                ),
              ),
          ],
        ),
        Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: AppSize.h10),
            child: RichText(
              text: TextSpan(
                text: '${context.l10n.customerBookingStatus}: ',
                style: context.textTheme.bodyMedium?.copyWith(
                  color: AppColors.ffADB5BD,
                ),
                children: [
                  TextSpan(
                    text: (widget.booking.status ?? '').upToLower,
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TitleInfoWidget(
                    title: context.l10n.customer,
                    subTitle:
                        '${widget.booking.booking?.customer?.firstName ?? ""}'
                        ' ${widget.booking.booking?.customer?.lastName ?? ""}',
                  ),
                  Gap(AppSize.h6),
                  Text.rich(
                    TextSpan(
                      text: '${context.l10n.no_of_vehicles}: ',
                      style: TextStyle(
                        color: AppColors.ffADB5BD,
                        fontSize: AppSize.sp12,
                        fontWeight: FontWeight.w500,
                      ),
                      children: [
                        TextSpan(
                          text: (widget.booking.bookedCars?.length ?? 0)
                              .toString(),
                          style: TextStyle(
                            color: AppColors.black,
                            fontSize: AppSize.sp12,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (widget.booking.booking?.customer?.bookingContactNumber !=
                          null &&
                      widget.booking.booking?.customer
                              ?.bookingContactNumberCountryCode !=
                          null)
                    AppPadding(
                      top: AppSize.h8,
                      child: Text.rich(
                        TextSpan(
                          text: '${context.l10n.contactNumber}: ',
                          style: TextStyle(
                            color: AppColors.ffADB5BD,
                            fontSize: AppSize.sp12,
                            fontWeight: FontWeight.w500,
                          ),
                          children: [
                            TextSpan(
                              text:
                                  '${widget.booking.booking?.customer?.bookingContactNumberCountryCode ?? ''} '
                                  '${widget.booking.booking?.customer?.bookingContactNumber ?? ''}',
                              style: TextStyle(
                                color: AppColors.primaryColor,
                                fontSize: AppSize.sp12,
                                fontWeight: FontWeight.w600,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  launchUrlString(
                                    'tel://${widget.booking.booking?.customer?.bookingContactNumberCountryCode}${widget.booking.booking?.customer?.bookingContactNumber}',
                                  );
                                },
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Builder(
              builder: (context) {
                double size = 0;
                for (final element
                    in widget.booking.bookedCars ?? <BookedCar>[]) {
                  size += element.car?.size ?? 0;
                }
                final tripPerCost = widget.tripDetailsModel.costPerKilometer;
                final sharedPrice = (tripPerCost ?? 0) *
                    (widget.booking.sharedBookings?.firstOrNull
                            ?.totalTripDistance ??
                        0) *
                    (widget.slots?? 0);
                final exclusivePrice = (widget
                            .booking.exclusiveBookings?.isNotEmpty ??
                        false)
                    ? '\$${((widget.tripDetailsModel.totalTripDistance ?? 0) * (tripPerCost ?? 0) * (widget.slots?? 0)).round()}'
                    : null;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      context.l10n.totalTripCost,
                      style: TextStyle(
                        color: AppColors.ffADB5BD,
                        fontSize: AppSize.sp12,
                      ),
                    ),
                    Gap(AppSize.h6),
                    Text(
                      exclusivePrice?.smartFormat() ??
                          '\$${sharedPrice.round()}'.smartFormat(),
                      style: context.textTheme.titleLarge?.copyWith(
                        color: AppColors.ff67509C,
                        fontWeight: FontWeight.w600,
                        fontSize: AppSize.sp16,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),

        if (isViewDetails &&
            (widget.booking.sharedBookings?.isNotEmpty ?? false))
          Container(
            width: double.maxFinite,
            // margin: EdgeInsets.only(bottom: AppSize.h8),
            padding: EdgeInsets.all(AppSize.sp16),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.all(
                Radius.circular(AppSize.r10),
              ),
            ),
            child: Column(
              spacing: AppSize.h10,
              children: [
                Row(
                  spacing: AppSize.w4,
                  children: [
                    AppAssets.iconsLocationOrigin.image(
                      height: AppSize.h14,
                      width: AppSize.w14,
                    ),
                    Text(
                      context.l10n.originStockLocation,
                      style: context.textTheme.titleMedium?.copyWith(
                        color: AppColors.black,
                        fontSize: AppSize.sp14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Container(
                  width: double.maxFinite,
                  margin: EdgeInsets.only(bottom: AppSize.h4),
                  decoration: BoxDecoration(
                    color: AppColors.ffF2EEF8,
                    borderRadius: BorderRadius.all(
                      Radius.circular(AppSize.r6),
                    ),
                  ),
                  padding: EdgeInsets.all(AppSize.sp14),
                  child: Builder(
                    builder: (context) {
                      final sharedData = widget.booking.sharedBookings
                          ?.firstOrNull?.intermediateStartStopLocation;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            sharedData?.stopLocation?.fullAddress ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: AppColors.black,
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                sharedData?.estimatedArrivalDate?.monthDate ??
                                    '',
                                style: context.textTheme.titleLarge?.copyWith(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.ff67509C,
                                ),
                              ),
                              Text(
                                '${(sharedData?.startStopLocationPerDayCharge ?? 0).round().toString().smartFormat()} /day',
                                style: context.textTheme.titleLarge?.copyWith(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.ff67509C,
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
                Row(
                  spacing: AppSize.w4,
                  children: [
                    AppAssets.iconsLocation.image(
                      height: AppSize.h14,
                      width: AppSize.w14,
                    ),
                    Text(
                      context.l10n.dropStockLocation,
                      style: context.textTheme.titleMedium?.copyWith(
                        color: AppColors.black,
                        fontSize: AppSize.sp14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Container(
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                    color: AppColors.ffF2EEF8,
                    borderRadius: BorderRadius.all(
                      Radius.circular(AppSize.r6),
                    ),
                  ),
                  padding: EdgeInsets.all(AppSize.sp14),
                  child: Builder(
                    builder: (context) {
                      final data = widget.booking.sharedBookings?.firstOrNull
                          ?.intermediateEndStopLocation;
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            data?.stopLocation?.fullAddress ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: AppColors.black,
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                data?.estimatedArrivalDate?.monthDate ?? '',
                                style: context.textTheme.titleLarge?.copyWith(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.ff67509C,
                                ),
                              ),
                              Text(
                                '${(data?.startStopLocationPerDayCharge ?? 0).round().toString().smartFormat()}/day',
                                style: context.textTheme.titleLarge?.copyWith(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.ff67509C,
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

        if (widget.booking.sharedBookings?.isNotEmpty ?? false)
          AppPadding(
            top: !isViewDetails ? AppSize.h10 : 0,
            child: GestureDetector(
              onTap: () => setState(() => isViewDetails = !isViewDetails),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    isViewDetails ? Icons.close : Icons.add,
                    color: AppColors.primaryColor,
                  ),
                  Text(
                    isViewDetails
                        ? context.l10n.close
                        : context.l10n.view_details,
                    style: context.textTheme.titleLarge?.copyWith(
                      color: AppColors.primaryColor,
                      fontSize: AppSize.sp16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        // Gap(
        //   (widget.booking.sharedBookings?.isNotEmpty ?? false)
        //       ? AppSize.h10
        //       : 0,
        // ),
      ],
    );
    return widget.bookingId != widget.booking.id
        ? Container(
            margin: EdgeInsets.only(top: AppSize.h16),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.all(
                Radius.circular(AppSize.r4),
              ),
            ),
            padding: EdgeInsets.all(AppSize.sp16),
            child: child,
          )
        : _BlinkWidget(child);
  }
}

class _BlinkWidget extends StatefulWidget {
  const _BlinkWidget(this.child);
  final Widget child;

  @override
  State<_BlinkWidget> createState() => __BlinkWidgetState();
}

class __BlinkWidgetState extends State<_BlinkWidget>
    with TickerProviderStateMixin {
  late Animation<Color?> animation;
  late Animation<double?> widthAnimation;
  late AnimationController controller;
  int count = 0;

  @override
  void initState() {
    super.initState();
    controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    final curve = CurvedAnimation(parent: controller, curve: Curves.linear);
    animation =
        ColorTween(begin: AppColors.ffADB5BD, end: Colors.red).animate(curve);
    widthAnimation = Tween<double>(begin: 1, end: 2).animate(curve);
    animation.addStatusListener((status) {
      if (count < 11) {
        count++;
        if (status == AnimationStatus.completed) {
          controller.reverse();
        } else if (status == AnimationStatus.dismissed) {
          controller.forward();
        }
      } else {
        controller.stop();
      }
      setState(() {});
    });
    controller.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Padding(
          padding: EdgeInsets.only(top: AppSize.h16),
          child: DecoratedBox(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(AppSize.r8),
              border: count < 11
                  ? Border.all(
                      color: animation.value ?? AppColors.ffADB5BD,
                      width: widthAnimation.value ?? 1,
                      //  ??
                      //     AppColors.ffADB5BD,
                    )
                  : null,
            ),
            child: Padding(
              padding: EdgeInsets.all(AppSize.sp16),
              child: child,
            ),
          ),
        );
      },
      child: widget.child,
    );
  }

  // double controllerVal() {
  //   print('==>>> ${_controller.value}');
  //   return switch (_controller.value) {
  //     >= 2 || < 4 => 1 - (_controller.value - 3).abs(),
  //     >= 4 || < 6 => 1 - (_controller.value - 5).abs(),
  //     _ => 1 - (_controller.value - 7).abs(),
  //   };
  // }
}
