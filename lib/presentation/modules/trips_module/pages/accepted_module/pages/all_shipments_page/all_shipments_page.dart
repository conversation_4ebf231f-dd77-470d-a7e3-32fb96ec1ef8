import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:readmore/readmore.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/route_pages/model/router_request_param.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/widgets/all_shipments_card.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/widgets/details_card_widgets.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/widgets/star_and_stop_location_full_address_widget.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/provider/accepted_details_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/app_string.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// App Shipments page ui
class AllShipmentsPage extends StatelessWidget {
  /// Constructor
  const AllShipmentsPage({
    super.key,
    required this.allShipmentsParams,
  });

  final AllShipmentsParams allShipmentsParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => TripDetailsProvider(allShipmentsParams.tripId),
      builder: (context, child) {
        final tripDetailsProvider = context.read<TripDetailsProvider>();

        return ValueListenableBuilder(
          valueListenable: tripDetailsProvider.isShowLoading,
          builder: (context, isShowLoader, child) {
            final isExclusive =
                tripDetailsProvider.tripDetailsModel?.tripType ==
                    TripType.EXCLUSIVE.name;
            return Scaffold(
              backgroundColor: AppColors.ffF8F9FA,
              appBar: CustomAppBar(
                title: context.l10n.allShipments,
                actions: [
                  if (tripDetailsProvider.tripDetailsModel != null)
                    Selector<TripDetailsProvider,
                        (List<BookingModel>, TripDetailsModel?)>(
                      selector: (context, provider) =>
                          (provider.bookingList, provider.tripDetailsModel),
                      builder: (context, bookingListData, child) {
                        final isOngoing = bookingListData.$2?.status ==
                            AcceptedTripType.ONGOING.name;
                        final isCompleted = bookingListData.$2?.status ==
                            AcceptedTripType.COMPLETED.name;

                        /// check if trip is out for delivery
                        /// for exclusive trip only
                        final isOutForDelivery = bookingListData
                                .$1
                                .firstOrNull
                                ?.exclusiveBookings
                                ?.firstOrNull
                                ?.isOutForDelivery ??
                            false;

                        /// check if remaining payment button is enable for
                        /// exclusive trip only
                        final isPaymentButtonEnable = bookingListData.$1
                                .firstOrNull?.isRemainingPaymentButtonEnabled ??
                            false;

                        /// check if last intermediate pickup point is moved to next stop
                        final isMovedToNext =
                            bookingListData.$2?.intermediatePickUpPoint
                                    ?.where(
                                      (e) =>
                                          e.stopLocationIndex ==
                                          tripDetailsProvider.tripDetailsModel
                                              ?.intermediatePickUpPoint?.length,
                                    )
                                    .firstOrNull
                                    ?.isMovedToNextStop ??
                                false;

                        /// widget
                        final Widget child =
                            //check if trip date is today or gretter than today's date only then show this widget
                            tripDetailsProvider.tripDetailsModel?.tripStartDate
                                        ?.formatDateTimeToLocalDate()
                                        ?.isAfter(DateTime.now()) ??
                                    false
                                ? const SizedBox.shrink()
                                : GestureDetector(
                                    onTap: () {
                                      if (!isExclusive && isMovedToNext) {
                                        tripDetailsProvider.startCompleteTrip(
                                          allShipmentsParams.tripId,
                                          isComplete: isOngoing,
                                          context: context,
                                        );
                                      } else if (isOngoing &&
                                          !isOutForDelivery) {
                                        tripDetailsProvider.outForDelivery(
                                          allShipmentsParams.tripId,
                                          context: context,
                                        );
                                      } else if (isOngoing &&
                                          !isPaymentButtonEnable) {
                                        tripDetailsProvider.readyToCollect(
                                          allShipmentsParams.tripId,
                                          context: context,
                                        );
                                      } else {
                                        tripDetailsProvider.startCompleteTrip(
                                          allShipmentsParams.tripId,
                                          isComplete: isOngoing,
                                          context: context,
                                        );
                                      }
                                    },
                                    child: Text(
                                      isOngoing
                                          ? isOutForDelivery
                                              ? isPaymentButtonEnable
                                                  ? context.l10n.completeTrip
                                                  : context.l10n.readyOfCollect
                                              : (!isExclusive && isMovedToNext)
                                                  ? context.l10n.completeTrip
                                                  : context.l10n.outForDelivery
                                          : context.l10n.startTrip,
                                      style: context.textTheme.titleLarge
                                          ?.copyWith(
                                        color: AppColors.primaryColor,
                                        fontWeight: FontWeight.w600,
                                        fontSize: AppSize.sp16,
                                      ),
                                    ),
                                  );
                        return (bookingListData.$1.isNotEmpty && !isCompleted)
                            ? (!isExclusive && (isMovedToNext || !isOngoing))
                                ? child
                                : isExclusive
                                    ? child
                                    : const SizedBox.shrink()
                            : const SizedBox.shrink();
                      },
                    ),
                ],
              ),
              body: AppLoader(
                isShowLoader: isShowLoader,
                child: SizedBox.expand(
                  child: EasyRefresh(
                    header: AppCommonFunctions.getLoadingHeader(),
                    footer: AppCommonFunctions.getLoadingFooter(),
                    controller: tripDetailsProvider.refreshController,
                    onRefresh: () =>
                        tripDetailsProvider.getData(allShipmentsParams.tripId),
                    child: Selector<TripDetailsProvider, TripDetailsModel?>(
                      selector: (context, provider) =>
                          provider.tripDetailsModel,
                      builder: (context, tripDetailsModel, _) {
                        return tripDetailsModel == null && !isShowLoader
                            ? ListView(
                                children: [
                                  SizedBox(
                                    height: MediaQuery.of(context).size.height *
                                        0.8,
                                    child: Center(
                                        child:
                                            Text(context.l10n.noTripDataFound)),
                                  ),
                                ],
                              )
                            : tripDetailsModel == null
                                ? const SizedBox.expand()
                                : SingleChildScrollView(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: AppSize.appPadding,
                                    ),
                                    child: Column(
                                      children: [
                                        AllShipmentsCard(
                                          // acceptedTripModelData: acceptedTripModelData,
                                          tripDetailsProvider:
                                              tripDetailsProvider,
                                          slots: allShipmentsParams.slots,
                                        ),

                                        /// google map route widget
                                        if ((tripDetailsModel
                                                    .intermediatePickUpPoint
                                                    ?.isNotEmpty ??
                                                false) ||
                                            (tripDetailsModel
                                                    .customPoints?.isNotEmpty ??
                                                false))
                                          Builder(
                                            builder: (context) {
                                              final intermediatePickUpPoint = [
                                                ...(tripDetailsModel
                                                        .intermediatePickUpPoint
                                                        ?.map(
                                                          (e) => e.stopLocation,
                                                        )
                                                        .toList() ??
                                                    []),
                                              ];
                                              return GestureDetector(
                                                onTap: () =>
                                                    AppNavigationService
                                                        .pushNamed(
                                                  context,
                                                  AppRoutes.tripsRouterScreen,
                                                  extra: RouterRequestParam(
                                                    addressList: [
                                                      ...(tripDetailsModel
                                                              .intermediatePickUpPoint
                                                              ?.map(
                                                                (e) => e
                                                                    .stopLocation,
                                                              )
                                                              .toList() ??
                                                          []),
                                                      ...(tripDetailsModel
                                                              .customPoints
                                                              ?.map(
                                                                (e) =>
                                                                    StopLocation(
                                                                  address: e,
                                                                ),
                                                              )
                                                              .toList() ??
                                                          []),
                                                    ],
                                                    destinationLocation:
                                                        intermediatePickUpPoint
                                                            .lastOrNull,
                                                  ),
                                                ),
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal: AppSize.w16,
                                                    vertical: AppSize.h10,
                                                  ),
                                                  margin: EdgeInsets.only(
                                                    bottom: AppSize.h16,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: AppColors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                      AppSize.r4,
                                                    ),
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      Icon(
                                                        Icons.route_rounded,
                                                        size: AppSize.sp22,
                                                      ),
                                                      Gap(AppSize.w6),
                                                      Text(
                                                        context.l10n
                                                            .yourEquipmentRoute,
                                                        style: context.textTheme
                                                            .bodyMedium
                                                            ?.copyWith(
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color:
                                                              AppColors.black,
                                                        ),
                                                      ),
                                                      const Spacer(),
                                                      Icon(
                                                        Icons
                                                            .arrow_forward_ios_rounded,
                                                        size: AppSize.sp16,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        Selector<TripDetailsProvider,
                                            List<BookingModel>>(
                                          selector: (context, provider) =>
                                              provider.bookingList,
                                          builder:
                                              (context, bookingList, child) {
                                            return StarAndStopLocationFullAddressWidget(
                                              // acceptedTripModel: acceptedTripModelData,
                                              booking: bookingList.firstOrNull,
                                              tripDetailsModel:
                                                  tripDetailsModel,
                                              isExclusive: isExclusive,
                                            );
                                          },
                                        ),
                                        Selector<TripDetailsProvider,
                                            List<BookingModel>>(
                                          selector: (context, provider) =>
                                              provider.bookingList,
                                          builder:
                                              (context, bookingList, child) {
                                            return Column(
                                              children: [
                                                for (final booking
                                                    in bookingList)
                                                  DetailsCardWidgets(
                                                    booking: booking,
                                                    isExclusive: isExclusive,
                                                    tripDetailsModel:
                                                        tripDetailsModel,
                                                    bookingId:
                                                        allShipmentsParams
                                                            .bookingId,
                                                    slots: allShipmentsParams
                                                        .slots,
                                                    onInit: (context) {
                                                      if (booking.id ==
                                                          allShipmentsParams
                                                              .bookingId) {
                                                        Scrollable
                                                            .ensureVisible(
                                                          context,
                                                          curve:
                                                              Curves.decelerate,
                                                          duration:
                                                              const Duration(
                                                            milliseconds: 800,
                                                          ),
                                                        );
                                                      }
                                                    },
                                                    // acceptedTripModelData: acceptedTripModelData,
                                                  ),
                                              ],
                                            );
                                          },
                                        ),
                                        Gap(AppSize.h16),
                                        if ((tripDetailsModel.reports ?? [])
                                            .isNotEmpty)
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                              horizontal: AppSize.w16,
                                              vertical: AppSize.h10,
                                            ),
                                            decoration: BoxDecoration(
                                              color: AppColors.white,
                                              borderRadius:
                                                  BorderRadius.circular(
                                                AppSize.r4,
                                              ),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Align(
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child: Padding(
                                                    padding: EdgeInsets.only(
                                                      bottom: AppSize.h5,
                                                    ),
                                                    child: Text(
                                                      l10n.reportsFromTransporter,
                                                      style: context
                                                          .textTheme.bodyLarge
                                                          ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                ListView.separated(
                                                  separatorBuilder:
                                                      (context, index) =>
                                                          Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                      horizontal: AppSize.h20,
                                                      vertical: AppSize.h2,
                                                    ),
                                                    child: const Divider(
                                                      color: AppColors.ffE6E6E6,
                                                    ),
                                                  ),
                                                  shrinkWrap: true,
                                                  itemCount: (tripDetailsModel
                                                              .reports ??
                                                          [])
                                                      .length,
                                                  padding: EdgeInsets.only(
                                                    top: AppSize.h5,
                                                  ),
                                                  physics:
                                                      const NeverScrollableScrollPhysics(),
                                                  itemBuilder:
                                                      (context, index) {
                                                    final report =
                                                        (tripDetailsModel
                                                                .reports ??
                                                            [])[index];

                                                    final delayTime =
                                                        tripDetailsProvider
                                                            .formateAffectedTime(
                                                      report.affectedTime,
                                                      context,
                                                    );

                                                    final listLength =
                                                        (tripDetailsModel
                                                                    .reports ??
                                                                [])
                                                            .length;
                                                    return Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      spacing: AppSize.h2,
                                                      children: [
                                                        Row(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Text(
                                                              listLength == 1
                                                                  ? AppStrings
                                                                      .arrow
                                                                  : '   ${index + 1}.  ',
                                                              style: context
                                                                  .textTheme
                                                                  .titleMedium
                                                                  ?.copyWith(
                                                                fontSize:
                                                                    AppSize
                                                                        .sp13,
                                                                color: AppColors
                                                                    .black,
                                                              ),
                                                            ),
                                                            Flexible(
                                                              child:
                                                                  ReadMoreText(
                                                                report.description ??
                                                                    '',
                                                                style: context
                                                                    .textTheme
                                                                    .titleMedium
                                                                    ?.copyWith(
                                                                  fontSize:
                                                                      AppSize
                                                                          .sp13,
                                                                  color:
                                                                      AppColors
                                                                          .black,
                                                                ),
                                                                trimMode:
                                                                    TrimMode
                                                                        .Line,
                                                                trimCollapsedText:
                                                                    context.l10n
                                                                        .readMore,
                                                                trimExpandedText:
                                                                    ' ${context.l10n.readLess}',
                                                                lessStyle: context
                                                                    .textTheme
                                                                    .titleMedium
                                                                    ?.copyWith(
                                                                  fontSize:
                                                                      AppSize
                                                                          .sp12,
                                                                  color: AppColors
                                                                      .ff0087C7,
                                                                ),
                                                                moreStyle: context
                                                                    .textTheme
                                                                    .titleMedium
                                                                    ?.copyWith(
                                                                  fontSize:
                                                                      AppSize
                                                                          .sp12,
                                                                  color: AppColors
                                                                      .ff0087C7,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                        if (delayTime
                                                            .isNotEmpty)
                                                          Align(
                                                            alignment: Alignment
                                                                .centerRight,
                                                            child: Text(
                                                              '${context.l10n.affectedTime}: '
                                                              '$delayTime',
                                                              style: context
                                                                  .textTheme
                                                                  .titleMedium
                                                                  ?.copyWith(
                                                                fontSize:
                                                                    AppSize
                                                                        .sp12,
                                                                color: AppColors
                                                                    .ff495057,
                                                              ),
                                                            ),
                                                          ),
                                                      ],
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        Gap(AppSize.h16),
                                        Builder(
                                          builder: (context) {
                                            final isCompleted = tripDetailsModel
                                                    .status ==
                                                AcceptedTripType.COMPLETED.name;
                                            return isCompleted
                                                ? const SizedBox.shrink()
                                                : Padding(
                                                    padding: EdgeInsets.only(
                                                      bottom: AppSize.h25,
                                                    ),
                                                    child: AppButton(
                                                      text: context
                                                          .l10n.cancelBooking,
                                                      onPressed: () {
                                                        context.showAlertDialog(
                                                          defaultActionText:
                                                              context.l10n.yes,
                                                          cancelActionText:
                                                              context.l10n.close,
                                                          onCancelActionPressed:
                                                              Navigator.pop,
                                                          onDefaultActionPressed:
                                                              (ctx) async {
                                                            AppNavigationService
                                                                .pop(context);
                                                            await tripDetailsProvider
                                                                .getCancelTrip(
                                                                    allShipmentsParams
                                                                            .tripId ??
                                                                        0,
                                                                    context);
                                                          },
                                                          titleWidget: Text(
                                                            context.l10n
                                                                .cancelTrip,
                                                            textAlign: TextAlign
                                                                .center,
                                                            style:
                                                                const TextStyle(
                                                              fontSize: 22,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              color: AppColors
                                                                  .ff6C757D,
                                                            ),
                                                          ),
                                                          contentWidget: Text(
                                                            context.l10n
                                                                .areUSureCancel,
                                                            textAlign: TextAlign
                                                                .center,
                                                            style:
                                                                const TextStyle(
                                                                    fontSize:
                                                                        15),
                                                          ),
                                                        );
                                                      },
                                                      isFillButton: false,
                                                      borderColor:
                                                          AppColors.red,
                                                      textStyle: context
                                                          .textTheme.titleLarge
                                                          ?.copyWith(
                                                        color: AppColors.red,
                                                        fontSize: AppSize.sp16,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  );
                                          },
                                        ),
                                      ],
                                    ),
                                  );
                      },
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
