import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/equipment_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/provider/accepted_trips_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/provider/exclusive_requested_trip_offer_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/app_string.dart';
import 'package:transportmatch_provider/widgets/app_dropdown.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';

/// Trip Info Widgets
class TripInfoWidgets extends StatelessWidget {
  /// Constructor
  const TripInfoWidgets({
    required this.readOnly,
    this.acceptedTripProvider,
    this.requestedTripProvider,
    required this.tripDataProvider,
    required this.exclusiveOfferProvider,
    super.key,
  });

  final bool readOnly;
  final AcceptedTripsProvider? acceptedTripProvider;
  final RequestedTripProvider? requestedTripProvider;
  final TripDataProvider tripDataProvider;
  final ExclusiveRequestedTripOfferProvider exclusiveOfferProvider;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSize.sp14),
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r10),
      ),
      child: Column(
        spacing: AppSize.h8,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.l10n.tripInfo,
            style: context.textTheme.titleLarge
                ?.copyWith(fontWeight: FontWeight.w700),
          ),
          ValueListenableBuilder(
            valueListenable: tripDataProvider.driverListModelList,
            builder: (context, driverListModelList, child) {
              return ValueListenableBuilder(
                valueListenable: tripDataProvider.equipmentListModelList,
                builder: (context, equipmentList, _) {
                  final isEquipmentAvailable = equipmentList.isNotEmpty;
                  final isDriverAvailable = driverListModelList.isNotEmpty;
                  final isAllAvailable =
                      isEquipmentAvailable && isDriverAvailable;
                  return isAllAvailable
                      ? child!
                      : Text(
                          AppStrings.arrowWithoutSpace +
                              context.l10n.noEquipmentDriver(
                                isEquipmentAvailable
                                    ? context.l10n.driver.toLowerCase()
                                    : context.l10n.equipment,
                              ),
                        );
                },
              );
            },
            child: Column(
              spacing: AppSize.h8,
              children: [
                ValueListenableBuilder(
                  valueListenable: tripDataProvider.equipmentListModelList,
                  builder: (context, equipmentList, _) {
                    return equipmentList.isEmpty
                        ? const SizedBox.shrink()
                        : AppDropdown(
                            title: context.l10n.selectEquipment,
                            controller: tripDataProvider.selectedEquipment,
                            hintText: context.l10n.chooseEquipment,
                            fillColor: AppColors.ffF8F9FA,
                            onChanged: (dropDownValue) {
                              if (dropDownValue != null &&
                                  dropDownValue is DropDownValueModel) {
                                tripDataProvider.selectedEquipment.setDropDown(
                                  dropDownValue,
                                );
                                // equipmentProvider.selectedEquipment = equipmentProvider
                                //     .equipmentListDataModel?.results
                                //     ?.firstWhere(
                                //   (element) => element.id.toString() == value,
                                // );

                                if (acceptedTripProvider != null) {
                                  acceptedTripProvider?.spotAvail.text =
                                      (dropDownValue.value
                                                  as EquipmentDropDownModel)
                                              .slot
                                              ?.toString() ??
                                          '';
                                } else if (requestedTripProvider != null) {
                                  requestedTripProvider?.spotAvail.text =
                                      (dropDownValue.value
                                                  as EquipmentDropDownModel)
                                              .slot
                                              ?.toString() ??
                                          '';
                                }
                                requestedTripProvider?.notify();
                                // equipmentProvider.notify();
                              }
                            },
                            // selectedItem:
                            //     equipmentProvider.selectedEquipment?.id.toString(),
                            items: equipmentList
                                .map(
                                  (e) => DropDownValueModel(
                                    value: e,
                                    name: e.name ?? '',
                                  ),
                                )
                                .toList(),
                          );
                  },
                ),
                AppTextFormField(
                  title: context.l10n.availableSlots,
                  controller: acceptedTripProvider?.spotAvail ??
                      requestedTripProvider?.spotAvail,
                  fillColor: AppColors.ffF8F9FA,
                  borderSide: const BorderSide(color: AppColors.ffDEE2E6),
                  keyboardType: TextInputType.number,
                  hintText: context.l10n.enterAvailableSlot,
                  maxTextLength: tripDataProvider
                      .selectedEquipment.dropDownValue?.value?.slot
                      .toString()
                      .length,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    TextInputFormatter.withFunction((oldValue, newValue) {
                      final text = newValue.text;
                      if (text == '0' ||
                          (text.length > 1 && text.startsWith('0'))) {
                        return oldValue;
                      }
                      return newValue;
                    }),
                  ],
                  onChanged: (p0) {
                    //calculate total cost if distance is greater than 0 and available slot is greater than 0 and km per is greater than 0
                    if (exclusiveOfferProvider.distance.value > 0 &&
                        requestedTripProvider != null &&
                        requestedTripProvider!.spotAvail.text.isNotEmpty &&
                        requestedTripProvider!
                            .costPerKmController.text.isNotEmpty) {
                      final costPerKm = double.tryParse(
                        requestedTripProvider!.costPerKmController.text,
                      );
                      if (costPerKm != null) {
                        final distanceInKm =
                            exclusiveOfferProvider.distance.value / 1000;
                        // Calculate total cost based on cost per km and distance
                        final totalCost = costPerKm * distanceInKm;
                        requestedTripProvider?.totalCostController.text =
                            (totalCost *
                                    (int.tryParse(
                                          requestedTripProvider!.spotAvail.text,
                                        ) ??
                                        1))
                                .toStringAsFixed(2); // Keep 2 decimal places
                      }
                    } else {
                      requestedTripProvider?.totalCostController.clear();
                      requestedTripProvider?.costPerKmController.clear();
                    }
                  },
                ),
                ValueListenableBuilder(
                  valueListenable: tripDataProvider.driverListModelList,
                  builder: (context, driverList, _) {
                    return driverList.isEmpty
                        ? const SizedBox.shrink()
                        : AppDropdown(
                            title: context.l10n.selectDriver,
                            controller: tripDataProvider.selectedDriver,
                            fillColor: AppColors.ffF8F9FA,
                            hintText: context.l10n.chooseDriver,
                            // onChanged: (dropdownValue) {
                            //   if (dropdownValue != null &&
                            //       dropdownValue is DropDownValueModel) {
                            //     // driverProvider.selectedDriver = driverProvider
                            //     //     .driverListModel?.results
                            //     //     .where((element) => element.id.toString() == value)
                            //     //     .firstOrNull;
                            //     // driverProvider.notify();
                            //     driverProvider.selectedDriver.setDropDown(dropdownValue);
                            //   }
                            // },
                            // selectedItem: driverProvider.selectedDriver?.id.toString(),
                            items: driverList
                                .map(
                                  (e) => DropDownValueModel(
                                    value: e,
                                    name: e.firstName ?? '',
                                  ),
                                )
                                .toList(),
                          );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
