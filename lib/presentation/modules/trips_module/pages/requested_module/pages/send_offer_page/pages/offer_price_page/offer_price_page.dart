
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/sent_offer_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/pages/offer_price_page/models/offer_price_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/provider/send_offer_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/widgets/change_offer_sheet.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

class OfferPricePage extends StatelessWidget {
  OfferPricePage({
    super.key,
    required this.offerPriceParams,
  }) : assert(
          offerPriceParams.tripId != null || offerPriceParams.offerData != null,
          'Either tripId or offerData must be provided',
        );
  final OfferPriceParams offerPriceParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => SendOfferProvider(
        tripId: offerPriceParams.tripId,
        offerData: offerPriceParams.offerData,
      ),
      child: Selector<SendOfferProvider, (bool, bool)>(
        selector: (context, sendOfferProvider) => (
          sendOfferProvider.getCurrentOffersLoad,
          sendOfferProvider.isDetailShowLoad,
        ),
        builder: (context, isLoading, child) {
          final sendOfferProvider = context.read<SendOfferProvider>();
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(
              title: context.l10n.sentOffers,
            ),
            body: ValueListenableBuilder(
              valueListenable: sendOfferProvider.sentOfferDetail,
              builder: (context, offerData, child) {
                return AppLoader(
                  isShowLoader: isLoading.$1 || isLoading.$2,
                  child: EasyRefresh(
                    header: AppCommonFunctions.getLoadingHeader(),
                    footer: AppCommonFunctions.getLoadingFooter(),
                    controller: sendOfferProvider.refreshController,
                    onRefresh: () =>
                        context.read<SendOfferProvider>().getSentOfferDetail(),
                    child: !(isLoading.$1 || isLoading.$2) && offerData == null
                        ? ListView(
                          children: [
                            SizedBox(
                              height: MediaQuery.of(context).size.height * 0.8,
                              child: Center(
                                  child: Text(context.l10n.noTripDataFound),
                                ),
                            ),
                          ],
                        )
                        : (isLoading.$1 || isLoading.$2)
                            ? const SizedBox.expand()
                            : SingleChildScrollView(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSize.appPadding,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    DecoratedBox(
                                      decoration: BoxDecoration(
                                        color: AppColors.white,
                                        borderRadius:
                                            BorderRadius.circular(AppSize.r4),
                                      ),
                                      child: Padding(
                                        padding: EdgeInsets.all(AppSize.sp16),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding: EdgeInsets.only(
                                                bottom: AppSize.h16,
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Flexible(
                                                    fit: FlexFit.tight,
                                                    child: Text(
                                                      context.l10n.transporter,
                                                      style: context
                                                          .textTheme.titleMedium
                                                          ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize: AppSize.sp16,
                                                        color: AppColors.black,
                                                      ),
                                                    ),
                                                  ),
                                                  Row(
                                                    children: [
                                                      AppAssets.iconsTimer
                                                          .image(
                                                        height: AppSize.h10,
                                                        width: AppSize.w12,
                                                        color:
                                                            AppColors.ff6C757D,
                                                      ),
                                                      Gap(AppSize.w2),
                                                      Builder(
                                                        builder: (context) {
                                                          final duration =
                                                              offerData
                                                                  ?.tripEndDate
                                                                  ?.difference(
                                                            DateTime.now(),
                                                          );
                                                          return Text(
                                                            '${duration?.inDays ?? 0} ${context.l10n.days},'
                                                            ' ${(duration?.inHours ?? 0) ~/ 60}${context.l10n.hr}',
                                                            style: TextStyle(
                                                              fontSize:
                                                                  AppSize.sp12,
                                                              color: AppColors
                                                                  .ff6C757D,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Flexible(
                                                  child: Column(
                                                    spacing: AppSize.h8,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      TitleInfoWidget(
                                                        title:
                                                            '${context.l10n.availableSlots}:',
                                                        subTitle:
                                                            '${offerData?.spotAvailableForReservation}',
                                                        subTitleFontWeight:
                                                            FontWeight.w600,
                                                        subTitleColor:
                                                            AppColors.ff67509C,
                                                        titleColor:
                                                            AppColors.ff6C757D,
                                                      ),
                                                      Selector<
                                                          SendOfferProvider,
                                                          List<
                                                              OfferProviderModel>>(
                                                        selector: (
                                                          context,
                                                          sendOfferProvider,
                                                        ) =>
                                                            sendOfferProvider
                                                                .offers,
                                                        builder: (
                                                          context,
                                                          offers,
                                                          child,
                                                        ) {
                                                          final index =
                                                              offers.indexWhere(
                                                            (x) =>
                                                                x.isOwner ??
                                                                false,
                                                          );
                                                          return index != -1
                                                              ? TitleInfoWidget(
                                                                  title: context
                                                                      .l10n
                                                                      .yourCurrentBid,
                                                                  subTitle:
                                                                      '${offers[index].offerPrice?.toInt()}'
                                                                          .smartFormat(),
                                                                  subTitleFontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  subTitleColor:
                                                                      AppColors
                                                                          .ff67509C,
                                                                  titleColor:
                                                                      AppColors
                                                                          .ff6C757D,
                                                                )
                                                              : const SizedBox();
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Flexible(
                                                  child: Column(
                                                    spacing: AppSize.h8,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      TitleInfoWidget(
                                                        title: context.l10n
                                                            .carsWantedToMove,
                                                        subTitle:
                                                            '${offerData?.totalCar?.toInt()}',
                                                        subTitleFontWeight:
                                                            FontWeight.w600,
                                                        subTitleColor:
                                                            AppColors.ff67509C,
                                                        titleColor:
                                                            AppColors.ff6C757D,
                                                      ),
                                                      Selector<
                                                          SendOfferProvider,
                                                          List<
                                                              OfferProviderModel>>(
                                                        selector: (
                                                          context,
                                                          sendOfferProvider,
                                                        ) =>
                                                            sendOfferProvider
                                                                .offers,
                                                        builder: (
                                                          context,
                                                          offers,
                                                          child,
                                                        ) {
                                                          final index =
                                                              offers.indexWhere(
                                                            (x) =>
                                                                x.isOwner ??
                                                                false,
                                                          );
                                                          return index != -1
                                                              ? TitleInfoWidget(
                                                                  title: context
                                                                      .l10n
                                                                      .yourCurrentSpot,
                                                                  subTitle:
                                                                      '# ${index + 1}',
                                                                  subTitleFontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  subTitleColor:
                                                                      AppColors
                                                                          .ff67509C,
                                                                  titleColor:
                                                                      AppColors
                                                                          .ff6C757D,
                                                                )
                                                              : const SizedBox();
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Selector<SendOfferProvider,
                                        List<OfferProviderModel>>(
                                      selector: (context, sendOfferProvider) =>
                                          sendOfferProvider.offers,
                                      builder: (context, offers, child) {
                                        return Column(
                                          children: [
                                            if (offers.isNotEmpty)
                                              Padding(
                                                padding: EdgeInsets.only(
                                                  top: AppSize.h16,
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  spacing: AppSize.w4,
                                                  children: [
                                                    Flexible(
                                                      child: Text(
                                                        context.l10n
                                                            .listOfCurrentOffers,
                                                        style: context.textTheme
                                                            .titleLarge
                                                            ?.copyWith(
                                                          fontSize:
                                                              AppSize.sp16,
                                                          fontWeight:
                                                              FontWeight.w700,
                                                        ),
                                                      ),
                                                    ),
                                                    Container(
                                                      decoration: BoxDecoration(
                                                        color:
                                                            AppColors.ffF2EEF8,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                          AppSize.r5,
                                                        ),
                                                      ),
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                        horizontal: AppSize.w12,
                                                        vertical: AppSize.h8,
                                                      ),
                                                      child: Text(
                                                        '${context.l10n.avgPrice}: '
                                                        '${sendOfferProvider.averageOffer.toString().smartFormat()}',
                                                        style: context.textTheme
                                                            .titleSmall
                                                            ?.copyWith(
                                                          fontSize:
                                                              AppSize.sp12,
                                                          color: AppColors
                                                              .ff67509C,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            Gap(AppSize.h8),
                                            if (offers.isNotEmpty)
                                              ListView.builder(
                                                physics:
                                                    const NeverScrollableScrollPhysics(),
                                                itemCount: offers.length,
                                                shrinkWrap: true,
                                                itemBuilder: (context, index) {
                                                  final offer = offers[index];
                                                  return Container(
                                                    decoration: BoxDecoration(
                                                      color: AppColors.white,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                        AppSize.r8,
                                                      ),
                                                    ),
                                                    padding: EdgeInsets.all(
                                                      AppSize.sp16,
                                                    ),
                                                    child: Row(
                                                      children: [
                                                        Flexible(
                                                          flex: 5,
                                                          fit: FlexFit.tight,
                                                          child: Text(
                                                            '${index + 1}. ${context.l10n.provider} ${index + 1} '
                                                            ' ${(offer.isOwner ?? false) ? '(${context.l10n.you})' : ''}',
                                                            style: context
                                                                .textTheme
                                                                .titleMedium
                                                                ?.copyWith(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w400,
                                                              fontSize:
                                                                  AppSize.sp16,
                                                            ),
                                                          ),
                                                        ),
                                                        Flexible(
                                                          flex: 2,
                                                          fit: FlexFit.tight,
                                                          child: Text(
                                                            '${offer.offerPrice?.toInt()}'
                                                                .smartFormat(),
                                                            style: context
                                                                .textTheme
                                                                .titleMedium
                                                                ?.copyWith(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w400,
                                                              fontSize:
                                                                  AppSize.sp16,
                                                            ),
                                                          ),
                                                        ),
                                                        Flexible(
                                                          flex: 2,
                                                          fit: FlexFit.tight,
                                                          child: Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .end,
                                                            children: [
                                                              const Icon(
                                                                Icons.star,
                                                                color: AppColors
                                                                    .warningColor,
                                                              ),
                                                              Gap(AppSize.w1),
                                                              Text(
                                                                offer.ratings
                                                                        ?.toStringAsFixed(
                                                                      1,
                                                                    ) ??
                                                                    '0',
                                                                style: context
                                                                    .textTheme
                                                                    .titleMedium
                                                                    ?.copyWith(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                  fontSize:
                                                                      AppSize
                                                                          .sp16,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                          ],
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                  ),
                );
              },
            ),
            bottomNavigationBar: ValueListenableBuilder(
              valueListenable: sendOfferProvider.sentOfferDetail,
              builder: (context, offerData, child) {
                return offerData == null || isLoading.$1 || isLoading.$2
                    ? const SizedBox.shrink()
                    : Padding(
                        padding: EdgeInsets.only(
                          top: AppSize.h5,
                          bottom: AppSize.h30,
                          left: AppSize.appPadding,
                          right: AppSize.appPadding,
                        ),
                        child: AppButton(
                          text: context.l10n.changeOfferPrice,
                          onPressed: () => showOfferBottomSheet(
                            context,
                            sendOfferProvider,
                            offerData,
                            offerSent: (newOffer) => context
                                .read<SendOfferProvider>()
                                .getSentOfferDetail(isWantShowLoader: true),
                          ),
                        ),
                      );
              },
            ),
          );
        },
      ),
    );
  }
}
