import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/common_card_widgets.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';

///Exclusive Trip Screen
class ExclusiveTripPage extends StatefulWidget {
  /// Constructor
  const ExclusiveTripPage({
    required this.requestedTripProvider,
    super.key,
    required this.tripDataProvider,
  });
  final RequestedTripProvider requestedTripProvider;
  final TripDataProvider tripDataProvider;

  @override
  State<ExclusiveTripPage> createState() => _ExclusiveTripPageState();
}

class _ExclusiveTripPageState extends State<ExclusiveTripPage> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        widget.requestedTripProvider
            .refreshExclusiveTrips(isWantShowLoader: true);
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable:
          widget.requestedTripProvider.isShowLoaderForExclusiveTrips,
      builder: (context, isShowLoader, child) {
        return AppLoader(
          isShowLoader: isShowLoader,
          child:
              Selector<RequestedTripProvider, (List<ExclusiveTrip>, String?)>(
            selector: (context, provider) => (
              provider.requestedExclusiveBookings,
              provider.exclusiveTripsNextUrl
            ),
            builder: (context, requestedExclusiveBookings, child) {
              final exclusiveTripList = requestedExclusiveBookings.$1;
              return EasyRefresh(
                header: AppCommonFunctions.getLoadingHeader(),
                footer: AppCommonFunctions.getLoadingFooter(),
                controller:
                    widget.requestedTripProvider.exclusiveTripRefreshController,
                onRefresh: () =>
                    widget.requestedTripProvider.refreshExclusiveTrips(),
                onLoad: requestedExclusiveBookings.$2.isNotEmptyAndNotNull
                    ? () => widget.requestedTripProvider
                            .getRequestedExclusiveBookings(
                          isPagination: true,
                        )
                    : null,
                child: exclusiveTripList.isEmpty && !isShowLoader
                    ? ListView(
                        children: [
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.7,
                            child: Center(
                              child: Text(
                                context.l10n.noExclusiveTripsFound,
                                style: context.textTheme.bodyMedium,
                              ),
                            ),
                          ),
                        ],
                      )
                    : ListView(
                        padding: EdgeInsets.only(
                          bottom: AppSize.h16,
                        ),
                        children: [
                          if (!isShowLoader || exclusiveTripList.isNotEmpty)
                            Text.rich(
                              TextSpan(
                                text: '${context.l10n.exclusiveTrip}: ',
                                style: context.textTheme.titleLarge?.copyWith(
                                  fontSize: AppSize.sp12,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.ff6C757D,
                                ),
                                children: [
                                  TextSpan(
                                    text: context.l10n.personalizedServiceText,
                                    style: TextStyle(
                                      color: AppColors.ff6C757D,
                                      fontWeight: FontWeight.w400,
                                      fontSize: AppSize.sp12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ListView.builder(
                            itemCount: exclusiveTripList.length,
                            shrinkWrap: true,
                            padding: EdgeInsets.only(top: AppSize.h16),
                            physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              final booking = exclusiveTripList[index];
                              return CommonCardWidgets(
                                value: 1,
                                onOfferPressed: () async {
                                  widget.tripDataProvider.selectedEquipment
                                      .clearDropDown();
                                  widget.tripDataProvider.selectedDriver
                                      .clearDropDown();
                                  widget.requestedTripProvider
                                    ..spotAvail.text = ''
                                    ..costPerKmController.text = ''
                                    ..totalCostController.text = ''
                                    ..date.value = null;

                                  final result =
                                      await AppNavigationService.pushNamed(
                                    context,
                                    AppRoutes.tripsExclusiveRequestedScreen,
                                    extra: ExclusiveRequestedParams(
                                      exclusiveBooking: booking,
                                      requestedTripProvider:
                                          widget.requestedTripProvider,
                                      tripDataProvider: widget.tripDataProvider,
                                    ),
                                    afterBack: (value) {
                                      if (value != null && value is bool) {
                                        if (value) {
                                          // refresh the list
                                          widget.requestedTripProvider
                                              .refreshExclusiveTrips();
                                        }
                                      }
                                    },
                                    // ExclusiveRequestedScreen(
                                    //   exclusiveBooking: booking,
                                    //   requestedTripProvider:
                                    //       widget.requestedTripProvider,
                                    //   tripDataProvider:
                                    //       widget.tripDataProvider,
                                    // ),
                                  );
                                },
                                onNotInterestedTripPressed: () => widget
                                    .requestedTripProvider
                                    .rejectExclusiveTrip(
                                  booking.id ?? 0,
                                ),
                                exclusiveBooking: booking,
                              );
                            },
                          ),
                        ],
                      ),
              );
            },
          ),
        );
      },
    );
  }
}
