import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/driver_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/equipment_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/provider/exclusive_requested_trip_offer_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/widgets/vehicle_info_widgets.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/trip_info_widgets.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/logger.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/app_tooltip.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// Exclusive Requested Screen
class ExclusiveRequestedTripOfferPage extends StatelessWidget {
  /// Constructor
  ExclusiveRequestedTripOfferPage({
    super.key,
    required this.exclusiveRequestedParams,
  }) : assert(
          exclusiveRequestedParams.tripId != null ||
              exclusiveRequestedParams.exclusiveBooking != null,
          'Either tripId or exclusiveBooking must be provided',
        );
  final ExclusiveRequestedParams exclusiveRequestedParams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.ffF8F9FA,
      appBar: CustomAppBar(
        title: exclusiveRequestedParams.isEditMode
            ? context.l10n.editOffer
            : context.l10n.requests,
      ),
      body: ChangeNotifierProvider(
        create: (context) => ExclusiveRequestedTripOfferProvider(
          exclusiveBooking: exclusiveRequestedParams.exclusiveBooking,
          exclusiveRequestedParams: exclusiveRequestedParams,
        ),
        child: Builder(
          builder: (context) {
            final exclusiveOfferProvider =
                context.read<ExclusiveRequestedTripOfferProvider>();
            return ChangeNotifierProvider.value(
              value: exclusiveRequestedParams.tripDataProvider,
              child: ValueListenableBuilder(
                valueListenable: exclusiveRequestedParams
                    .requestedTripProvider.isDetailShowLoader,
                builder: (context, isDetailShowLoader, child) {
                  return ValueListenableBuilder(
                    valueListenable: exclusiveOfferProvider.isShowLoader,
                    builder: (context, isShowLoader, _) {
                      return AppLoader(
                        isShowLoader: isShowLoader || isDetailShowLoader,
                        child: !isDetailShowLoader &&
                                exclusiveOfferProvider.exclusiveBooking == null
                            ? Center(
                                child: Text(context.l10n.noTripDataFound),
                              )
                            : isDetailShowLoader
                                ? const SizedBox.expand()
                                : child!,
                      );
                    },
                  );
                },
                child: Consumer<ExclusiveRequestedTripOfferProvider>(
                  builder: (context, exclusiveOffer, child) {
                    return SingleChildScrollView(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSize.appPadding,
                      ),
                      child: Column(
                        spacing: AppSize.h16,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (!exclusiveRequestedParams.isEditMode)
                            VehiclesInfoWidget(
                              data:
                                  exclusiveOffer.exclusiveBooking?.carDetails ??
                                      [],
                            ),
                          TripInfoWidgets(
                            readOnly: false,
                            requestedTripProvider:
                                exclusiveRequestedParams.requestedTripProvider,
                            tripDataProvider:
                                exclusiveRequestedParams.tripDataProvider,
                            exclusiveOfferProvider: exclusiveOffer,
                          ),
                          AbsorbPointer(
                            absorbing: exclusiveRequestedParams.isEditMode,
                            child: Opacity(
                              opacity:
                                  exclusiveRequestedParams.isEditMode ? 0.5 : 1,
                              child: Container(
                                padding: EdgeInsets.all(AppSize.sp14),
                                width: double.maxFinite,
                                decoration: BoxDecoration(
                                  color: AppColors.white,
                                  borderRadius:
                                      BorderRadius.circular(AppSize.r10),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  spacing: AppSize.h10,
                                  children: [
                                    Text(
                                      context.l10n.tripStartEndDates,
                                      style: context.textTheme.titleLarge
                                          ?.copyWith(
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                    AppTextFormField(
                                      title: context.l10n.startDate,
                                      hintText: 'DD/MM/YYYY',
                                      fillColor: AppColors.ffF8F9FA,
                                      readOnly: true,
                                      controller: TextEditingController(
                                        text: exclusiveOffer.exclusiveBooking
                                            ?.customerStartDate?.dateOnly,
                                      ),
                                      borderSide: const BorderSide(
                                        color: AppColors.ffDEE2E6,
                                      ),
                                      suffixIcon: AppAssets.iconsCalender.image(
                                        height: AppSize.h20,
                                        width: AppSize.w20,
                                        color: AppColors.black,
                                      ),
                                    ),
                                    AppTextFormField(
                                      title: context.l10n.endDate,
                                      controller: TextEditingController(
                                        text: exclusiveOffer.exclusiveBooking
                                            ?.customerEndDate?.dateOnly,
                                      ),
                                      readOnly: true,
                                      fillColor: AppColors.ffF8F9FA,
                                      borderSide: const BorderSide(
                                        color: AppColors.ffDEE2E6,
                                      ),
                                      hintText: 'DD/MM/YYYY',
                                      suffixIcon: AppAssets.iconsCalender.image(
                                        height: AppSize.h20,
                                        width: AppSize.w20,
                                        color: AppColors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Selector<ExclusiveRequestedTripOfferProvider,
                              List<Marker>>(
                            selector: (context, exclusiveOfferProvider) =>
                                exclusiveOfferProvider.markers,
                            builder: (context, value, child) {
                              return ClipRRect(
                                borderRadius:
                                    BorderRadius.circular(AppSize.r10),
                                child: SizedBox(
                                  height: context.height / 2.5,
                                  width: context.width,
                                  child: ValueListenableBuilder(
                                    valueListenable:
                                        exclusiveOfferProvider.polyline,
                                    builder: (context, polyLine, child) {
                                      return GoogleMap(
                                        // mapType: MapType.hybrid,
                                        initialCameraPosition:
                                            exclusiveOfferProvider
                                                .googleMapInitial,
                                        markers: value.toSet(),
                                        polylines: polyLine.toSet(),
                                        onMapCreated: (cont) =>
                                            exclusiveOfferProvider.onMapCreated(
                                          cont,
                                          newAssignData: exclusiveOfferProvider
                                              .exclusiveBooking!,
                                        ),
                                        onTap: (argument) {
                                          argument.logFatal;
                                        },
                                        gestureRecognizers: {}..add(
                                            const Factory<
                                                OneSequenceGestureRecognizer>(
                                              EagerGestureRecognizer.new,
                                            ),
                                          ),
                                      );
                                    },
                                  ),
                                ),
                              );
                            },
                          ),
                          AbsorbPointer(
                            absorbing: exclusiveRequestedParams.isEditMode,
                            child: Opacity(
                              opacity:
                                  exclusiveRequestedParams.isEditMode ? 0.5 : 1,
                              child: ChangeNotifierProvider.value(
                                value: exclusiveRequestedParams
                                    .requestedTripProvider,
                                child: Consumer<RequestedTripProvider>(
                                  builder:
                                      (context, requestedTripProvider, child) {
                                    return Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.all(AppSize.r6)
                                              .add(EdgeInsets.only(
                                                  top: AppSize.h10,),),
                                          child: Text.rich(
                                            TextSpan(
                                              text: context.l10n.enterCost,
                                              style: context
                                                  .textTheme.titleMedium
                                                  ?.copyWith(
                                                fontSize: AppSize.sp15,
                                                fontWeight: FontWeight.w600,
                                              ),
                                              children: [
                                                TextSpan(
                                                  text: context.l10n.perKm,
                                                  style: context
                                                      .textTheme.titleMedium
                                                      ?.copyWith(
                                                    fontSize: AppSize.sp15,
                                                    fontWeight: FontWeight.w600,
                                                    color: AppColors.ffADB5BD,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        AppTextFormField(
                                          hintText: r'E.g $2.5',
                                          controller: exclusiveRequestedParams
                                              .requestedTripProvider
                                              .costPerKmController,
                                          keyboardType: TextInputType.number,
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                              RegExp(r'^\d*\.?\d{0,2}$'),
                                            ),
                                          ],
                                          suffixIcon: AppTooltip(
                                            msg: context.l10n.costPerKmTooltip,
                                          ),
                                          readOnly: exclusiveRequestedParams
                                                  .isEditMode ||
                                              exclusiveOfferProvider
                                                      .distance.value <=
                                                  0 ||
                                              (exclusiveRequestedParams
                                                      .requestedTripProvider
                                                      .spotAvail
                                                      .text
                                                      .isEmpty ||
                                                  exclusiveRequestedParams
                                                          .tripDataProvider
                                                          .selectedEquipment
                                                          .dropDownValue ==
                                                      null),
                                          onTap: () {
                                            if (exclusiveRequestedParams
                                                    .requestedTripProvider
                                                    .spotAvail
                                                    .text
                                                    .isEmpty ||
                                                exclusiveRequestedParams
                                                        .tripDataProvider
                                                        .selectedEquipment
                                                        .dropDownValue ==
                                                    null) {
                                              context.l10n
                                                  .pleaseSelectEquipmentAndAvailableSlot
                                                  .showErrorAlert();
                                            } else if (exclusiveOfferProvider
                                                    .distance.value <=
                                                0) {
                                              context.l10n
                                                  .tripDistanceIsZeroPleaseSelectOriginDropLocation
                                                  .showErrorAlert();
                                            }
                                          },
                                          onChanged: (p0) {
                                            if (p0 != null &&
                                                p0.isNotEmpty &&
                                                exclusiveOfferProvider
                                                        .distance.value >
                                                    0) {
                                              final costPerKm =
                                                  double.tryParse(p0);
                                              if (costPerKm != null) {
                                                final distanceInKm =
                                                    exclusiveOfferProvider
                                                            .distance.value /
                                                        1000;
                                                // Calculate total cost based on cost per km and distance
                                                final totalCost =
                                                    costPerKm * distanceInKm;
                                                exclusiveRequestedParams
                                                    .requestedTripProvider
                                                    .totalCostController
                                                    .text = (totalCost *
                                                        (int.tryParse(
                                                              exclusiveRequestedParams
                                                                  .requestedTripProvider
                                                                  .spotAvail
                                                                  .text,
                                                            ) ??
                                                            1))
                                                    .toStringAsFixed(
                                                  2,
                                                ); // Keep 2 decimal places
                                              }
                                            } else {
                                              exclusiveRequestedParams
                                                  .requestedTripProvider
                                                  .totalCostController
                                                  .clear();
                                            }
                                          },
                                        ),
                                        Padding(
                                          padding: EdgeInsets.all(AppSize.r6)
                                              .add(EdgeInsets.only(
                                                  top: AppSize.h10,),),
                                          child: Text.rich(
                                            TextSpan(
                                              text: context.l10n.totalCost,
                                              style: context
                                                  .textTheme.titleMedium
                                                  ?.copyWith(
                                                fontSize: AppSize.sp15,
                                                fontWeight: FontWeight.w600,
                                              ),
                                              // children: [
                                              // TextSpan(
                                              //   text: context.l10n.perSlot,
                                              //   style:
                                              //       context.textTheme.titleMedium?.copyWith(
                                              //     fontSize: AppSize.sp15,
                                              //     fontWeight: FontWeight.w600,
                                              //     color: AppColors.ffADB5BD,
                                              //   ),
                                              // ),
                                              // ],
                                            ),
                                          ),
                                        ),
                                        AppTextFormField(
                                          // title: context.l10n.totalCost,
                                          hintText: context.l10n.enterTotalCost,
                                          controller: exclusiveRequestedParams
                                              .requestedTripProvider
                                              .totalCostController,
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                              RegExp(r'^\d*\.?\d{0,2}$'),
                                            ),
                                          ],
                                          keyboardType: TextInputType.number,
                                          suffixIcon: AppTooltip(
                                            msg: context
                                                .l10n.totalCostOfTripTooltip,
                                          ),
                                          readOnly: exclusiveRequestedParams
                                                  .isEditMode ||
                                              exclusiveOfferProvider
                                                      .distance.value <=
                                                  0 ||
                                              (exclusiveRequestedParams
                                                      .requestedTripProvider
                                                      .spotAvail
                                                      .text
                                                      .isEmpty ||
                                                  exclusiveRequestedParams
                                                          .tripDataProvider
                                                          .selectedEquipment
                                                          .dropDownValue ==
                                                      null),
                                          onTap: () {
                                            if (exclusiveRequestedParams
                                                    .requestedTripProvider
                                                    .spotAvail
                                                    .text
                                                    .isEmpty ||
                                                exclusiveRequestedParams
                                                        .tripDataProvider
                                                        .selectedEquipment
                                                        .dropDownValue ==
                                                    null) {
                                              context.l10n
                                                  .pleaseSelectEquipmentAndAvailableSlot
                                                  .showErrorAlert();
                                            } else if (exclusiveOfferProvider
                                                    .distance.value <=
                                                0) {
                                              context.l10n
                                                  .tripDistanceIsZeroPleaseSelectOriginDropLocation
                                                  .showErrorAlert();
                                            }
                                          },
                                          onChanged: (p0) {
                                            if (p0 != null &&
                                                p0.isNotEmpty &&
                                                exclusiveOfferProvider
                                                        .distance.value >
                                                    0) {
                                              final totalCost =
                                                  double.tryParse(p0);
                                              if (totalCost != null) {
                                                final distanceInKm =
                                                    exclusiveOfferProvider
                                                            .distance.value /
                                                        1000;
                                                if (distanceInKm > 0) {
                                                  // Update cost per km field
                                                  final costPerKm =
                                                      totalCost / distanceInKm;
                                                  exclusiveRequestedParams
                                                      .requestedTripProvider
                                                      .costPerKmController
                                                      .text = (costPerKm /
                                                          (int.tryParse(
                                                                exclusiveRequestedParams
                                                                    .requestedTripProvider
                                                                    .spotAvail
                                                                    .text,
                                                              ) ??
                                                              1))
                                                      .toStringAsFixed(
                                                    2,
                                                  ); // Keep 2 decimal places
                                                }
                                              }
                                            } else {
                                              exclusiveRequestedParams
                                                  .requestedTripProvider
                                                  .costPerKmController
                                                  .clear();
                                            }
                                          },
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                          AppPadding.symmetric(
                            vertical: AppSize.h20,
                            child: AppButton(
                              text: exclusiveRequestedParams.isEditMode
                                  ? context.l10n.updateOffer
                                  : context.l10n.sendOffer,
                              onPressed: () async {
                                if (exclusiveRequestedParams.isEditMode) {
                                  // Update existing offer
                                  await exclusiveOfferProvider
                                      .updateExclusiveTripOffer(
                                    context,
                                    equipmentId: (exclusiveRequestedParams
                                                    .tripDataProvider
                                                    .selectedEquipment
                                                    .dropDownValue
                                                    ?.value
                                                as EquipmentDropDownModel?)
                                            ?.id ??
                                        0,
                                    driverId: (exclusiveRequestedParams
                                                .tripDataProvider
                                                .selectedDriver
                                                .dropDownValue
                                                ?.value as DriverDropDownModel?)
                                            ?.id ??
                                        0,
                                    spotAvailableForReservation: int.tryParse(
                                          exclusiveRequestedParams
                                              .requestedTripProvider
                                              .spotAvail
                                              .text,
                                        ) ??
                                        0,
                                    tripId: exclusiveRequestedParams
                                            .exclusiveBooking?.id
                                            .toString() ??
                                        '',
                                  );
                                } else {
                                  // Create new offer
                                  await exclusiveOfferProvider
                                      .createExclusiveTripOffer(
                                    context,
                                    startDate: (exclusiveOfferProvider
                                                .exclusiveBooking
                                                ?.customerStartDate ??
                                            DateTime.now())
                                        .toUtc(),
                                    endDate: (exclusiveOfferProvider
                                                .exclusiveBooking
                                                ?.customerEndDate ??
                                            DateTime.now())
                                        .toUtc(),
                                    equipmentId: (exclusiveRequestedParams
                                                    .tripDataProvider
                                                    .selectedEquipment
                                                    .dropDownValue
                                                    ?.value
                                                as EquipmentDropDownModel?)
                                            ?.id ??
                                        0,
                                    driverId: (exclusiveRequestedParams
                                                .tripDataProvider
                                                .selectedDriver
                                                .dropDownValue
                                                ?.value as DriverDropDownModel?)
                                            ?.id ??
                                        0,
                                    spotAvailableForReservation: int.tryParse(
                                          exclusiveRequestedParams
                                              .requestedTripProvider
                                              .spotAvail
                                              .text,
                                        ) ??
                                        0,
                                    totalTripDistance:
                                        exclusiveOfferProvider.distance.value,
                                    costPerKilometer: double.tryParse(
                                          exclusiveRequestedParams
                                              .requestedTripProvider
                                              .costPerKmController
                                              .text,
                                        ) ??
                                        0,
                                    bookingId: exclusiveOfferProvider
                                            .exclusiveBooking?.id ??
                                        0,
                                  );
                                }
                              },
                            ),
                          ),
                          Gap(AppSize.h10),
                        ],
                      ),
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
