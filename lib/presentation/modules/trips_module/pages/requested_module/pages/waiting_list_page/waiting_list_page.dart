// import 'package:flutter/material.dart';
// import 'package:easy_refresh/easy_refresh.dart';
// import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
// import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/common_card_widgets.dart';
// import 'package:transportmatch_provider/router/app_navigation_service.dart';
// import 'package:transportmatch_provider/router/app_routes.dart';

// import 'package:transportmatch_provider/utils/app_size.dart';

// /// Waiting List Screen
// class WaitingListPage extends StatefulWidget {
//   /// Constructor
//   const WaitingListPage(this.requestedTripProvider, {super.key});
//   final RequestedTripProvider requestedTripProvider;

//   @override
//   State<WaitingListPage> createState() => _ExclusiveTripScreenState();
// }

// class _ExclusiveTripScreenState extends State<WaitingListPage> {
//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       widget.requestedTripProvider.refreshWaitingList();
//     });
//   }

//   final refreshController = EasyRefreshController();
//   @override
//   void dispose() {
//     refreshController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return EasyRefresh(
                                // header: Platform.isAndroid? const MaterialHeader() : const CupertinoHeader(
                                //   foregroundColor: AppColors.primaryColor,
                                // ),
                                // footer: const CupertinoFooter(
                                //   foregroundColor: AppColors.primaryColor,
                                // ),
//       controller: refreshController,
//       onRefresh: () {
//         refreshController.refreshCompleted();
//         widget.requestedTripProvider.refreshWaitingList();
//       },
//       child: ListView.builder(
//         itemCount: 5,
//         padding: EdgeInsets.only(top: AppSize.h16),
//         itemBuilder: (context, index) {
//           return GestureDetector(
//             onTap: () {
//               AppNavigationService.pushNamed(
//                 context,
//                 AppRoutes.tripsWaitingRequestedScreen,
//               );
//             },
//             child: const CommonCardWidgets(
//               value: 2,
//               vehiclesCount: 10,
//               startTitle: 'Otay Mesa',
//               startDate: 'Nov 21',
//               endTitle: 'California',
//               endDate: 'Nov 28',
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
