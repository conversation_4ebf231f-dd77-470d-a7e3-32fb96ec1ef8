import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/driver_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/equipment_dropdown_data_model.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class TripDataProvider extends ChangeNotifier {
  bool isClosed = false;
  // ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? getDriversCancelToken;
  CancelToken? getEquipmentCancelToken;

  final ValueNotifier<List<DriverDropDownModel>> driverListModelList =
      ValueNotifier([]);
  final ValueNotifier<List<EquipmentDropDownModel>> equipmentListModelList =
      ValueNotifier([]);

  final SingleValueDropDownController selectedEquipment =
      SingleValueDropDownController();
  final SingleValueDropDownController selectedDriver =
      SingleValueDropDownController();

  Future<void> getDropDownListApiCall({
    required String startDate,
    required String endDate,
    required ValueNotifier<bool> isShowLoader,
    required bool isWinchRequired,
  }) async {
    '==>> getDropDownListApiCall $startDate $endDate'.logE;
    if (isClosed || (startDate.isEmptyOrNull && endDate.isEmptyOrNull)) return;
    try {
      isShowLoader.value = true;
      await Future.wait([
        getDriversDropDownListApiCall(
          startDate: startDate,
          endDate: endDate,
        ),
        getEquipmentDropDownListApiCall(
          startDate: startDate,
          endDate: endDate,
          isWinchRequired: isWinchRequired,
        ),
      ]);
      // overlay?.hide();
      isShowLoader.value = false;
    } catch (e) {
      isShowLoader.value = false;
      // overlay?.hide();
      if (isClosed) return;
      e.toString().logE;
    }
  }

  Future<void> getDriversDropDownListApiCall({
    String? startDate,
    String? endDate,
  }) async {
    if (isClosed) return;
    try {
      getDriversCancelToken?.cancel();
      getDriversCancelToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.driverDropDownData,
        cancelToken: getDriversCancelToken,
        params: {
          ApiKeys.startDate: startDate,
          ApiKeys.endDate: endDate,
        },
      );

      final res = await Injector.instance<TripRepository>()
          .getDriverDropDownData(request);

      await res.when(
        success: (data) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          driverListModelList.value.clear();
          driverListModelList.value = data;
          driverListModelList.notifyListeners();
          // notify();
        },
        error: (exception) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
      e.toString().logE;
    }
  }

  Future<void> getEquipmentDropDownListApiCall({
    String? startDate,
    String? endDate,
    required bool isWinchRequired,
  }) async {
    if (isClosed) return;
    try {
      getEquipmentCancelToken?.cancel();
      getEquipmentCancelToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.equipmentDropDownData,
        cancelToken: getEquipmentCancelToken,
        params: {
          if(isWinchRequired)
            ApiKeys.winch : true,
          ApiKeys.startDate: startDate,
          ApiKeys.endDate: endDate,

        },

      );

      final res = await Injector.instance<TripRepository>()
          .getEquipmentDropDownData(request);

      await res.when(
        success: (data) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          equipmentListModelList.value.clear();
          equipmentListModelList.value = data;
          equipmentListModelList.notifyListeners();
          // notify();
        },
        error: (exception) async {
          if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getDriversCancelToken?.isCancelled ?? true)) return;
      e.toString().logE;
    }
  }

  // void notify() {
  //   if (isClosed) return;
  //   try {
  //     notifyListeners();
  //   } catch (e) {
  //     '==>> notify error $e'.logE;
  //   }
  // }

  @override
  void dispose() {
    isClosed = true;
    getDriversCancelToken?.cancel();
    getEquipmentCancelToken?.cancel();
    driverListModelList.dispose();
    equipmentListModelList.dispose();
    // isShowLoader.dispose();
    super.dispose();
  }
}
