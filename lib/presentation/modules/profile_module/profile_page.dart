import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/db/app_db.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/models/signup_and_signin_model.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/provider/profile_provider.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/widgets/custom_tile_widget.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/widgets/separator_widget.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';

import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';
import 'package:transportmatch_provider/widgets/language_change_sheet.dart';

/// ProfileScreen
class ProfilePage extends StatelessWidget {
  /// ProfileScreen
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final isProvider =
        Injector.instance<AppDB>().userModel?.user?.role?.toLowerCase() ==
            UserType.Provider.name.toLowerCase();
    final l10n = context.l10n;
    return ChangeNotifierProvider(
      create: (context) => ProfileProvider(),
      child: Consumer<ProfileProvider>(
        builder: (context, profileProvider, _) {
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: isProvider
                ? null
                : CustomAppBar(
                    canPop: false,
                    title: l10n.myProfile,
                  ),
            body: SafeArea(
              child: ValueListenableBuilder(
                valueListenable: profileProvider.isShowLoader,
                builder: (context, loading, child) {
                  return AppLoader(
                    isShowLoader: loading,
                    child: child!,
                  );
                },
                child: ListView(
                  padding: EdgeInsets.all(AppSize.appPadding),
                  children: [
                    SeparatorWidget(
                      children: [
                        Selector<ProfileProvider, User?>(
                          selector: (p0, p1) => p1.user,
                          builder: (context, user, child) {
                            return CustomTileWidget(
                              onTap: () async {
                                await AppNavigationService.pushNamed(
                                  context,
                                  AppRoutes.profileEditScreen,
                                  afterBack: (value) {
                                    if (value != null && value is User) {
                                      context.read<ProfileProvider>()
                                        ..user = value
                                        ..notify();
                                    }
                                  },

                                  // ChangeNotifierProvider.value(
                                  //   value: profileProvider,
                                  //   child: const EditProfileScreen(),
                                  // ),
                                );
                              },
                              customWidget: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    user?.firstName ?? '',
                                    style:
                                        context.textTheme.titleLarge?.copyWith(
                                      fontSize: AppSize.sp16,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  Text(
                                    user?.email ?? '',
                                    style:
                                        context.textTheme.bodyMedium?.copyWith(
                                      color: AppColors.ffADB5BD,
                                      fontSize: AppSize.sp12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              suffix: AppAssets.iconsEditProfile.image(
                                height: AppSize.h18,
                                width: AppSize.h18,
                              ),
                            );
                          },
                        ),
                        // if (isProvider)
                        //   CustomTileWidget(
                        //     onTap: () {
                        //       AppNavigationService.pushNamed(
                        //         context,
                        //         const SignupProviderInfoScreen(
                        //           // isEditScreen: true,
                        //         ),
                        //       );
                        //     },
                        //     name: l10n.businessDetails,
                        //   ),
                        // if (isProvider)
                        //   CustomTileWidget(
                        //     onTap: () {
                        //       AppNavigationService.pushNamed(
                        //         context,
                        //         const TransactionScreen(),
                        //       );
                        //     },
                        //     name: l10n.transactions,
                        //   ),
                        if (isProvider)
                          CustomTileWidget(
                            onTap: () => profileProvider
                                .stripeConnectAccountLoginUrl(context),
                            name: l10n.stripeDashboard.upperCamelCase,
                          ),
                        if (isProvider)
                          CustomTileWidget(
                            onTap: () {
                              AppNavigationService.pushNamed(
                                context,
                                AppRoutes.profileDriverListScreen,
                              );
                            },
                            name: l10n.drivers,
                          ),
                        if (isProvider)
                          CustomTileWidget(
                            onTap: () {
                              AppNavigationService.pushNamed(
                                context,
                                AppRoutes.profileCancelTripListScreen,
                              );
                            },
                            name: l10n.cancelTrips,
                          ),
                        if (isProvider)
                          CustomTileWidget(
                            onTap: () {
                              AppNavigationService.pushNamed(
                                context,
                                AppRoutes.profileSavedRouteScreen,
                              );
                            },
                            name: l10n.savedRoute,
                          ),
                        CustomTileWidget(
                          onTap: () => showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder: (context) => const LanguageChangeSheet(),
                          ),
                          name: l10n.language,
                        ),
                        CustomTileWidget(
                          onTap: () {
                            AppNavigationService.pushNamed(
                              context,
                              AppRoutes.profileCustomerSupportScreen,
                            );
                          },
                          name: l10n.customerSupport,
                        ),
                        CustomTileWidget(
                          onTap: () async {
                            await context.showAlertDialog(
                              title: l10n.signOut,
                              content: l10n.signOutContent,
                              defaultActionText: l10n.signOut,
                              cancelActionText: l10n.cancel,
                              cancelActionBgColor: AppColors.white,
                              onCancelActionPressed: Navigator.pop,
                              onDefaultActionPressed: (dialogContext) {
                                Navigator.pop(dialogContext);
                                profileProvider.logout();
                              },
                            );
                          },
                          name: l10n.signOut,
                          fontColor: AppColors.errorColor,
                          suffix: AppAssets.iconsSignout.image(
                            height: AppSize.h20,
                            width: AppSize.h20,
                          ),
                        ),
                        if (isProvider)
                          Align(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              child: InkWell(
                                onTap: () {
                                  context.showAlertDialog(
                                    title: l10n.deleteAccount,
                                    content: l10n.deleteAccountContent,
                                    defaultActionText: l10n.delete,
                                    cancelActionText: l10n.cancel,
                                    cancelActionBgColor: AppColors.white,
                                    onCancelActionPressed: Navigator.pop,
                                    onDefaultActionPressed: (dialogContext) {
                                      Navigator.pop(dialogContext);
                                      profileProvider.deleteAccount();
                                    },
                                  );
                                },
                                child: Text(
                                  l10n.deleteAccount,
                                  style: context.textTheme.bodyMedium?.copyWith(
                                    fontSize: AppSize.sp16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.errorColor,
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
