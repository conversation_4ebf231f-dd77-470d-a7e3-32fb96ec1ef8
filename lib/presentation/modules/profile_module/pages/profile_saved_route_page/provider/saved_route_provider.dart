// ignore_for_file: public_member_api_docs

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class SavedRouterProvider extends ChangeNotifier {
  SavedRouterProvider() {
    getSavedTripList(isWantShowLoader: true);
  }

  bool isClosed = false;
  ValueNotifier<List<SavedRoute>> savedRouteList =
      ValueNotifier(<SavedRoute>[]);
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  final refreshController = EasyRefreshController();

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  CancelToken? getSavedTripToken;
  Future<void> getSavedTripList({
    bool isWantShowLoader = false,
  }) async {
    if (isClosed) return;
    try {
      getSavedTripToken?.cancel();
      getSavedTripToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.getSavedTripList,
        cancelToken: getSavedTripToken,
      );
      if (isWantShowLoader) {
        isShowLoader.value = true;
      }
      final res =
          await Injector.instance<TripRepository>().getSavedTripList(request);
      if (isClosed || (getSavedTripToken?.isCancelled ?? true)) {
        return;
      }
      if (isWantShowLoader) {
        isShowLoader.value = false;
      }
      await res.when(
        success: (data) async {
          if (isClosed || (getSavedTripToken?.isCancelled ?? true)) {
            return;
          }
          for (final datas in data.results ?? <SavedRoute>[]) {
            if (!savedRouteList.value.any((e) => e.id == datas.id)) {
              savedRouteList.value.add(datas);
            }
          }
          notify();
        },
        error: (exception) async {
          if (isClosed || (getSavedTripToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getSavedTripToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      e.logE;
      // e.toString().showErrorAlert();
    }
  }

  CancelToken? deleteRouteToken;
  Future<void> deleteSaveRoute(String id) async {
    if (isClosed) return;
    try {
      deleteRouteToken?.cancel();
      deleteRouteToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.deleteSaveRoute(id),
        cancelToken: deleteRouteToken,
      );
      isShowLoader.value = true;
      final res =
          await Injector.instance<TripRepository>().deleteSaveRoute(request);
      if (isClosed || (deleteRouteToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      await res.when(
        success: (data) async {
          if (isClosed || (deleteRouteToken?.isCancelled ?? true)) {
            return;
          }
          savedRouteList.value.removeWhere((e) => e.id?.toString() == id);
          notify();
          // ignore: unnecessary_cast
        },
        error: (exception) async {
          if (isClosed || (deleteRouteToken?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (deleteRouteToken?.isCancelled ?? true)) {
        return;
      }
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    getSavedTripToken?.cancel();
    deleteRouteToken?.cancel();
    isShowLoader.dispose();
    savedRouteList.dispose();
    refreshController.dispose();
    super.dispose();
  }
}
