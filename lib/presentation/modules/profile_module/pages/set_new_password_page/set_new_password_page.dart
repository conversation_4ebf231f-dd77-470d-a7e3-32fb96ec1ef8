// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/set_new_password_page/provider/set_new_password_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/utils/validators/password_match_validator.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class SetNewPasswordPage extends StatelessWidget {
  const SetNewPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Scaffold(
      appBar: CustomAppBar(
        title: l10n.editDetails,
      ),
      backgroundColor: AppColors.ffF8F9FA,
      body: ChangeNotifierProvider(
        create: (context) => SetNewPasswordProvider(),
        child: Consumer<SetNewPasswordProvider>(
          builder: (context, setNewPasswordProvider, _) {
            return ValueListenableBuilder<bool>(
              valueListenable: setNewPasswordProvider.isShowLoader,
              builder: (context, loading, child) {
                return AppLoader(
                  isShowLoader: loading,
                  child: child!,
                );
              },
              child: GestureDetector(
                onTap: AppCommonFunctions.closeKeyboard,
                child: AppPadding.symmetric(
                  horizontal: AppSize.appPadding,
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return SingleChildScrollView(
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: constraints.maxHeight,
                          ),
                          child: Form(
                            key: setNewPasswordProvider.formKeyResetPassword,
                            child: IntrinsicHeight(
                              child: AppPadding.symmetric(
                                vertical: AppSize.appPadding,
                                child: Column(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            context.l10n.setPassword,
                                            style: context
                                                .textTheme.headlineLarge
                                                ?.copyWith(
                                              fontSize: AppSize.sp24,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                          Gap(AppSize.h8),
                                          Text(
                                            context
                                                .l10n.goAheadAndSetANewPassword,
                                            style: context.textTheme.bodySmall
                                                ?.copyWith(
                                              fontSize: AppSize.sp16,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          Gap(AppSize.h20),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            spacing: AppSize.h20,
                                            children: [
                                              ValueListenableBuilder(
                                                valueListenable:
                                                    setNewPasswordProvider
                                                        .isOldPasswordShow,
                                                builder: (
                                                  context,
                                                  isOldPasswordShow,
                                                  _,
                                                ) {
                                                  return AppTextFormField(
                                                    title: l10n.oldPassword,
                                                    hintText:
                                                        l10n.enterYourPassword,
                                                    textAction:TextInputAction.next,
                                                    fillColor:
                                                        AppColors.ffF8F9FA,
                                                    obscureText:
                                                        !setNewPasswordProvider
                                                            .isOldPasswordShow
                                                            .value,
                                                    controller:
                                                        setNewPasswordProvider
                                                            .oldPasswordController,
                                                    validator: (p0) =>
                                                        passwordValidator()
                                                            .call(p0),
                                                    suffixIcon: GestureDetector(
                                                      onTap: () {
                                                        setNewPasswordProvider
                                                                .isOldPasswordShow
                                                                .value =
                                                            !setNewPasswordProvider
                                                                .isOldPasswordShow
                                                                .value;
                                                      },
                                                      child: isOldPasswordShow
                                                          ? const Icon(
                                                              Icons
                                                                  .visibility_outlined,
                                                            )
                                                          : const Icon(
                                                              Icons
                                                                  .visibility_off_outlined,
                                                            ),
                                                    ),
                                                  );
                                                },
                                              ),
                                              //* Password

                                              ValueListenableBuilder(
                                                valueListenable:
                                                    setNewPasswordProvider
                                                        .isPasswordShow,
                                                builder: (
                                                  context,
                                                  isPasswordShow,
                                                  _,
                                                ) {
                                                  return AppTextFormField(
                                                    title: l10n.password,
                                                    hintText:
                                                        l10n.enterYourPassword,
                                                    textAction:TextInputAction.next,
                                                    fillColor:
                                                        AppColors.ffF8F9FA,
                                                    obscureText:
                                                        !setNewPasswordProvider
                                                            .isPasswordShow
                                                            .value,
                                                    controller:
                                                        setNewPasswordProvider
                                                            .passwordController,
                                                    validator: (p0) =>
                                                        passwordValidator()
                                                            .call(p0),
                                                    suffixIcon: GestureDetector(
                                                      onTap: () {
                                                        setNewPasswordProvider
                                                                .isPasswordShow
                                                                .value =
                                                            !setNewPasswordProvider
                                                                .isPasswordShow
                                                                .value;
                                                      },
                                                      child: isPasswordShow
                                                          ? const Icon(
                                                              Icons
                                                                  .visibility_outlined,
                                                            )
                                                          : const Icon(
                                                              Icons
                                                                  .visibility_off_outlined,
                                                            ),
                                                    ),
                                                  );
                                                },
                                              ),

                                              //* Confirm Password

                                              ValueListenableBuilder(
                                                valueListenable:
                                                    setNewPasswordProvider
                                                        .isConfirmPasswordShow,
                                                builder: (
                                                  context,
                                                  isConfirmPasswordShow,
                                                  _,
                                                ) {
                                                  return AppTextFormField(
                                                    title: l10n.confirmPassword,
                                                    hintText:
                                                        l10n.enterYourPassword,
                                                    fillColor:
                                                        AppColors.ffF8F9FA,
                                                    obscureText:
                                                        !setNewPasswordProvider
                                                            .isConfirmPasswordShow
                                                            .value,
                                                    controller:
                                                        setNewPasswordProvider
                                                            .confirmPasswordController,
                                                    validator: (value) =>
                                                        ConfirmPasswordValidator(
                                                      errorText: l10n
                                                          .pleaseEnterConfirmPassword,
                                                      password:
                                                          setNewPasswordProvider
                                                              .passwordController
                                                              .text,
                                                    ).call(value),
                                                    suffixIcon: GestureDetector(
                                                      onTap: () {
                                                        setNewPasswordProvider
                                                                .isConfirmPasswordShow
                                                                .value =
                                                            !setNewPasswordProvider
                                                                .isConfirmPasswordShow
                                                                .value;
                                                      },
                                                      child:
                                                          isConfirmPasswordShow
                                                              ? const Icon(
                                                                  Icons
                                                                      .visibility_outlined,
                                                                )
                                                              : const Icon(
                                                                  Icons
                                                                      .visibility_off_outlined,
                                                                ),
                                                    ),
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                          Gap(AppSize.h16),
                                        ],
                                      ),
                                    ),
                                    AppButton(
                                      text: l10n.changePassword,
                                      onPressed: () {
                                        if (setNewPasswordProvider.formKeyResetPassword.currentState?.validate() ?? false) {
                                          if (setNewPasswordProvider
                                                  .confirmPasswordController
                                                  .text ==
                                              setNewPasswordProvider
                                                  .oldPasswordController.text) {
                                            l10n.newPasswordAndOldPasswordCannotBeSame
                                                .showErrorAlert();
                                          } else {
                                            setNewPasswordProvider
                                                .resetPasswordAPIcall(
                                              context: context,
                                            );
                                          }
                                        }
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
