import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/edit_profile_page/provider/edit_profile_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';

import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// EditProfileScreen
class EditProfilePage extends StatelessWidget {
  /// EditProfileScreen
  const EditProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return ChangeNotifierProvider(
      create: (context) => EditProfileProvider(),
      child: Consumer<EditProfileProvider>(
        builder: (context, editProfileProvider, child) {
          return GestureDetector(
            onTap: AppCommonFunctions.closeKeyboard,
            child: Scaffold(
              appBar: CustomAppBar(
                title: l10n.editDetails,
              ),
              backgroundColor: AppColors.ffF8F9FA,
              body: ValueListenableBuilder<bool>(
                valueListenable: editProfileProvider.isShowLoader,
                builder: (context, loading, child) {
                  return AppLoader(
                    isShowLoader: loading,
                    child: child!,
                  );
                },
                child: SafeArea(
                  child: AppPadding.all(
                    padding: AppSize.appPadding,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        return SingleChildScrollView(
                          child: IntrinsicHeight(
                            child: ConstrainedBox(
                              constraints: BoxConstraints(
                                minHeight: constraints.maxHeight,
                              ),
                              child: Column(
                                children: [
                                  Expanded(
                                    child: Form(
                                      key: editProfileProvider.formKey,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        spacing: AppSize.h14,
                                        children: [
                                          AppTextFormField(
                                            title: l10n.name,
                                            hintText: l10n.enterYourName,
                                            textAction:TextInputAction.next,
                                            fillColor: AppColors.ffF8F9FA,
                                            controller: editProfileProvider
                                                .nameController,
                                            validator: (p0) =>
                                                nameValidator().call(p0),
                                          ),

                                          //* Email

                                          AppTextFormField(
                                            title: l10n.email,
                                            controller: editProfileProvider
                                                .emailController,
                                            hintText: l10n.enterYourEmail,
                                            textAction:TextInputAction.next,
                                            keyboardType:
                                                TextInputType.emailAddress,
                                            fillColor: AppColors.ffE6E6E6,
                                            readOnly: true,
                                            textColor: AppColors.ffADB5BD,
                                            borderSide: BorderSide(
                                              width: AppSize.w1,
                                              color: AppColors.ffADB5BD,
                                            ),
                                          ),

                                          //* Password

                                          AppTextFormField(
                                            title: l10n.password,
                                            controller: editProfileProvider
                                                .passwordController,
                                            hintText: l10n.enterYourPassword,
                                            fillColor: AppColors.ffE6E6E6,
                                            textAction:TextInputAction.next,
                                            readOnly: true,
                                            obscureText: true,
                                            textColor: AppColors.ffADB5BD,
                                            borderSide: BorderSide(
                                              width: AppSize.w1,
                                              color: AppColors.ffADB5BD,
                                            ),
                                          ),
                                          Align(
                                            alignment: Alignment.centerRight,
                                            child: InkWell(
                                              onTap: () {
                                                AppNavigationService.pushNamed(
                                                  context,
                                                  AppRoutes
                                                      .profileSetNewPasswordScreen,
                                                );
                                              },
                                              child: Text(
                                                l10n.changePassword,
                                                style: context
                                                    .textTheme.bodyLarge
                                                    ?.copyWith(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: AppSize.sp12,
                                                  color: AppColors.ff0087C7,
                                                  decorationColor:
                                                      AppColors.ff0087C7,
                                                  decoration:
                                                      TextDecoration.underline,
                                                ),
                                              ),
                                            ),
                                          ),

                                          //* Company Name
                                          AppTextFormField(
                                            title: l10n.companyName,
                                            hintText: l10n.enterYourCompanyName,
                                            textAction: TextInputAction.next,
                                            fillColor: AppColors.ffE6E6E6,
                                            readOnly: true,
                                            textColor: AppColors.ffADB5BD,
                                            borderSide: BorderSide(
                                              width: AppSize.w1,
                                              color: AppColors.ffADB5BD,
                                            ),
                                            controller: editProfileProvider
                                                .companyTextController,
                                            validator: (p0) =>
                                                companyNameValidator().call(p0),
                                          ),

                                          //* Commercial Name
                                          AppTextFormField(
                                            title: l10n.commercialName,
                                            hintText: l10n.enterCommercialName,
                                            fillColor: AppColors.ffE6E6E6,
                                            textAction:TextInputAction.next,
                                            readOnly: true,
                                            textColor: AppColors.ffADB5BD,
                                            borderSide: BorderSide(
                                              width: AppSize.w1,
                                              color: AppColors.ffADB5BD,
                                            ),
                                            controller: editProfileProvider
                                                .commercialTextController,
                                            validator: (p0) =>
                                                commercialNameValidator()
                                                    .call(p0),
                                          ),

                                          //* Tax ID
                                          AppTextFormField(
                                            title: l10n.taxID,
                                            hintText: l10n.enterTaxID,
                                            fillColor: AppColors.ffE6E6E6,
                                            textAction:TextInputAction.next,
                                            readOnly: true,
                                            textColor: AppColors.ffADB5BD,
                                            borderSide: BorderSide(
                                              width: AppSize.w1,
                                              color: AppColors.ffADB5BD,
                                            ),
                                            controller: editProfileProvider
                                                .taxIdTextController,
                                            validator: (p0) =>
                                                taxIDValidator().call(p0),
                                          ),

                                          //* Webpage
                                          if (editProfileProvider
                                              .webPageTextController
                                              .text
                                              .isNotEmpty)
                                            Row(
                                              children: [
                                                Text(
                                                  '  ${l10n.webpage}',
                                                  textAlign: TextAlign.center,
                                                  style: context
                                                      .textTheme.bodyMedium
                                                      ?.copyWith(
                                                    fontWeight: FontWeight.w500,
                                                    fontSize: AppSize.sp14,
                                                    color: AppColors.black,
                                                  ),
                                                ),
                                                Text(
                                                  ' (${l10n.optional})',
                                                  textAlign: TextAlign.center,
                                                  style: context
                                                      .textTheme.bodyMedium
                                                      ?.copyWith(
                                                    fontWeight: FontWeight.w500,
                                                    fontSize: AppSize.sp14,
                                                    color: AppColors.ffADB5BD,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          if (editProfileProvider
                                              .webPageTextController
                                              .text
                                              .isNotEmpty)
                                            AppPadding(
                                              top: AppSize.h4,
                                              child: AppTextFormField(
                                                hintText:
                                                    l10n.enterYourWebpageURL,
                                                fillColor: AppColors.ffE6E6E6,
                                                readOnly: true,
                                                textColor: AppColors.ffADB5BD,
                                                borderSide: BorderSide(
                                                  width: AppSize.w1,
                                                  color: AppColors.ffADB5BD,
                                                ),
                                                controller: editProfileProvider
                                                    .webPageTextController,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  AppButton(
                                    text: l10n.save,
                                    onPressed: () async {
                                      if (editProfileProvider.formKey.currentState?.validate() ?? false) {
                                        await editProfileProvider
                                            .updateUserData(
                                          context: context,
                                        );
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
