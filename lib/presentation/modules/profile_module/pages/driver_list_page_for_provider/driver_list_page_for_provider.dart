// ignore_for_file: public_member_api_docs


import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/driver_list_page_for_provider/provider/driver_list_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class DriverListPageForProvider extends StatelessWidget {
  const DriverListPageForProvider({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ChangeNotifierProvider<DriverListProvider>(
      create: (context) => DriverListProvider(),
      child: Consumer<DriverListProvider>(
        builder: (context, driverListProvider, _) {
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(
              title: l10n.allDrivers,
              actions: [
                IconButton(
                  onPressed: () async {
                    await AppNavigationService.pushNamed(
                      context,
                      AppRoutes.profileDriverAddScreen,
                      afterBack: (value) {
                        if (value != null && value is bool) {
                          if (value) {
                            driverListProvider.refreshDriverListData(
                              isShowLoader: true,
                            );
                          }
                        }
                      },
                    );
                  },
                  icon: Icon(
                    Icons.add,
                    color: AppColors.primaryColor,
                    size: AppSize.sp28,
                  ),
                ),
              ],
            ),
            body: ValueListenableBuilder(
              valueListenable: driverListProvider.isShowLoader,
              builder: (context, isShowLoader, child) {
                return AppLoader(
                  isShowLoader: isShowLoader,
                  child: child!,
                );
              },
              child: (driverListProvider.driverListModel != null)
                  ? EasyRefresh(
                   header: AppCommonFunctions.getLoadingHeader(),
                   footer: AppCommonFunctions.getLoadingFooter(),
                   controller: driverListProvider.refreshController,
                   onRefresh: () =>
                          driverListProvider.refreshDriverListData(),
                    onLoad: driverListProvider.nextUrl.isNotEmptyAndNotNull
                          ? () => driverListProvider.getDriversApiCall(
                                nextUrl: driverListProvider.nextUrl,
                                isPagination: true,
                              )
                          : null,
                      child: (driverListProvider
                              .driverListModel!.results.isEmpty)
                          ? ValueListenableBuilder(
                              valueListenable:
                                  driverListProvider.isShowLoader,
                              builder: (context, isShowLoader, child) {
                                return isShowLoader
                                    ? const SizedBox.expand()
                                    : ListView(
                                      children: [
                                        SizedBox(
                                          height: MediaQuery.of(context).size.height * 0.75,
                                          child: Center(
                                              child: Text(
                                                l10n.noDriverAvailableYet,
                                                style: context
                                                    .theme.textTheme.displaySmall,
                                              ),
                                            ),
                                        ),
                                      ],
                                    );
                              },
                            )
                          : ListView.builder(
                              physics: const AlwaysScrollableScrollPhysics(),
                              padding: EdgeInsets.symmetric(
                                vertical: AppSize.h16,
                              ),
                              itemCount: driverListProvider
                                  .driverListModel?.results.length,
                              itemBuilder: (context, index) {
                                final driverData = driverListProvider
                                    .driverListModel?.results[index];
                                return Column(
                                  children: [
                                    Container(
                                      margin: EdgeInsets.only(
                                        bottom: AppSize.h16,
                                      ),
                                      padding: EdgeInsets.all(
                                        AppSize.h16,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppColors.white,
                                        borderRadius: BorderRadius.circular(
                                          AppSize.r4,
                                        ),
                                      ),
                                      child: Column(
                                        children: [
                                          Row(
                                            children: [
                                              Container(
                                                padding: EdgeInsets.all(
                                                  AppSize.sp16,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: AppColors.ffF8F9FA,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                    AppSize.r4,
                                                  ),
                                                ),
                                                child: AppAssets.iconsPerson
                                                    .image(
                                                  height: AppSize.h20,
                                                  width: AppSize.h20,
                                                ),
                                              ),
                                              Expanded(
                                                child: AppPadding.symmetric(
                                                  horizontal: AppSize.w8,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceEvenly,
                                                    children: [
                                                      Text(
                                                        l10n.name,
                                                        style: context
                                                            .textTheme
                                                            .bodyMedium
                                                            ?.copyWith(
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          fontSize:
                                                              AppSize.sp12,
                                                          color: AppColors
                                                              .ff6C757D,
                                                        ),
                                                      ),
                                                      Gap(AppSize.h2),
                                                      Text(
                                                        driverData
                                                                ?.firstName ??
                                                            '-',
                                                        maxLines: 2,
                                                        style: context
                                                            .textTheme
                                                            .bodyMedium
                                                            ?.copyWith(
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          fontSize:
                                                              AppSize.sp16,
                                                          color: AppColors
                                                              .ff343A40,
                                                          overflow:
                                                              TextOverflow
                                                                  .ellipsis,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              GestureDetector(
                                                onTap: () async {
                                                  await context
                                                      .showAlertDialog(
                                                    title: l10n.deleteDriver,
                                                    content: l10n
                                                        .deleteDriverConfirmation,
                                                    defaultActionText:
                                                        context.l10n.yes,
                                                    cancelActionText:
                                                        l10n.cancel,
                                                    cancelActionBgColor:
                                                        AppColors.white,
                                                    onCancelActionPressed:
                                                        Navigator.pop,
                                                    onDefaultActionPressed:
                                                        (dialogContext) {
                                                      Navigator.pop(
                                                        dialogContext,
                                                      );
                                                      driverListProvider
                                                          .deleteDriverApiCall(
                                                        context: context,
                                                        id: driverData?.id ??
                                                            -1,
                                                        index: index,
                                                      );
                                                    },
                                                  );
                                                },
                                                child: AppAssets.iconsDelete
                                                    .image(
                                                  height: AppSize.h20,
                                                ),
                                              ),
                                            ],
                                          ),
                                          Gap(AppSize.h16),
                                          Row(
                                            spacing: AppSize.w10,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment
                                                          .start,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceEvenly,
                                                  spacing: AppSize.h2,
                                                  children: [
                                                    Text(
                                                      l10n.email,
                                                      style: context.textTheme
                                                          .bodyMedium
                                                          ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize:
                                                            AppSize.sp12,
                                                        color: AppColors
                                                            .ff6C757D,
                                                      ),
                                                    ),
                                                    Text(
                                                      driverData?.email ??
                                                          '-',
                                                      maxLines: 2,
                                                      style: context.textTheme
                                                          .bodyMedium
                                                          ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize:
                                                            AppSize.sp16,
                                                        color: AppColors
                                                            .ff343A40,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceEvenly,
                                                spacing: AppSize.h2,
                                                children: [
                                                  Text(
                                                    l10n.dateAdded,
                                                    style: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: AppSize.sp12,
                                                      color:
                                                          AppColors.ff6C757D,
                                                    ),
                                                  ),
                                                  Text(
                                                    (driverData?.dateJoined
                                                                .isNotEmptyAndNotNull ??
                                                            false)
                                                        ? DateFormat.yMd()
                                                            .format(
                                                            driverData
                                                                    ?.dateJoined
                                                                    .formatDateTimeToLocalDate() ??
                                                                DateTime
                                                                    .now(),
                                                          )
                                                        : '-',
                                                    maxLines: 2,
                                                    style: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      fontSize: AppSize.sp16,
                                                      color:
                                                          AppColors.ff343A40,
                                                      overflow: TextOverflow
                                                          .ellipsis,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (index ==
                                            driverListProvider
                                                    .driverListModel!
                                                    .results
                                                    .length -
                                                1 &&
                                        (driverListProvider.driverListModel!
                                            .next.isNotEmptyAndNotNull)) ...{
                                      SizedBox(
                                        height: AppSize.h50,
                                        width: context.width,
                                        child: const Center(
                                          child: CircularProgressIndicator(),
                                        ),
                                      ),
                                    },
                                  ],
                                );
                              },
                            ),
                    )
                  : const SizedBox.expand(),
            ),
          );
        },
      ),
    );
  }
}
