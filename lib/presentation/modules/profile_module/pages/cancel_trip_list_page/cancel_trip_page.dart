
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/widgets/trip_card_widget.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/cancel_trip_list_page/provider/cancel_trip_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class CancelTripPage extends StatefulWidget {
  const CancelTripPage({super.key});

  @override
  State<CancelTripPage> createState() => _CancelTripPageState();
}

class _CancelTripPageState extends State<CancelTripPage> {


  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<CancelTripProvider>(
      create: (con) => CancelTripProvider()..getActiveTrips(isWantShowLoader: true),
      child: Scaffold(
        backgroundColor: AppColors.pageBGColor,
        appBar: CustomAppBar(
          title: context.l10n.cancelTrips,
        ),
        body: Builder(
          builder: (con) {
            return AppPadding.symmetric(
              horizontal: AppSize.appPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: AppSize.h10,),
                  Flexible(
                    child: ValueListenableBuilder<bool>(
                      valueListenable: con.read<CancelTripProvider>().isLoading,
                      builder: (context, isLoading, child) {
                        return AppLoader(
                          isShowLoader: isLoading &&
                              context.read<CancelTripProvider>().activeTrips.isEmpty,
                          child: Consumer<CancelTripProvider>(
                            builder: (context, cancelTripProvider, _) {
                              return EasyRefresh(
                                header: AppCommonFunctions.getLoadingHeader(),
                                footer: AppCommonFunctions.getLoadingFooter(),
                                controller: cancelTripProvider.refreshController,
                                onRefresh: () => cancelTripProvider.getActiveTrips(),
                                onLoad: cancelTripProvider.hasMoreData
                                    ? () => cancelTripProvider.getActiveTrips(
                                  isPagination: true,
                                )
                                    : null,
                                child: cancelTripProvider.activeTrips.isEmpty &&
                                    !isLoading
                                    ? ListView(
                                  children: [
                                    SizedBox(
                                      height: context.height * 0.7,
                                      child: Center(
                                        child: Text(
                                          context.l10n
                                              .noUpcomingTripAvailableYet,
                                          textAlign: TextAlign.center,
                                          style: context
                                              .textTheme.bodyMedium,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                                    : ListView.builder(
                                  itemCount:
                                  cancelTripProvider.activeTrips.length,
                                  padding: EdgeInsets.only(
                                    bottom: AppSize.h16,
                                  ),
                                  itemBuilder: (context, index) {
                                    final tripData =
                                    cancelTripProvider.activeTrips[index];
                                    return TripCard(
                                      tripData: tripData,
                                      onTap: () =>
                                          AppNavigationService.pushNamed(
                                            context,
                                            AppRoutes.tripsAllShipmentsScreen,
                                            extra: AllShipmentsParams(
                                              tripId: tripData.id,
                                              slots: tripData
                                                  .spotAvailableForReservation,
                                            ),
                                          ),
                                    );
                                  },
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
