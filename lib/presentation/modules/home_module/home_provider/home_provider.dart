import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class HomeProvider extends ChangeNotifier {
  bool isClosed = false;

  // Active trips list and pagination
  final List<AcceptedTripModelData> activeTrips = [];
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  String? nextUrl;
  CancelToken? activeTripsToken;
  final EasyRefreshController refreshController = EasyRefreshController();

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// Fetch active trips with pagination support
  Future<void> getActiveTrips({
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    if (isClosed) return;

    try {
      if (!isPagination) {
        nextUrl = null;
      }

      // Cancel previous request if any
      activeTripsToken?.cancel();
      activeTripsToken = CancelToken();
      if (isWantShowLoader) isLoading.value = true;

      final result = await Injector.instance<TripRepository>().getAcceptedTrips(
        ApiRequest(
          path: nextUrl ?? EndPoints.getAcceptedTrips('ACTIVE'),
          cancelToken: activeTripsToken,
        ),
      );

      await result.when(
        success: (data) async {
          if (isClosed || (activeTripsToken?.isCancelled ?? true)) {
            return;
          }
          data.toJson().logD;

          if (!isPagination) {
            activeTrips.clear();
          }

          activeTrips.addAll(data.results ?? []);
          nextUrl = data.next;
          notify();
        },
        error: (exception) async {
          if (isClosed || (activeTripsToken?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed) return;
      e.toString().logE;
    } finally {
      isLoading.value = false;
    }
    return;
  }

  /// Check if more data is available for pagination
  bool get hasMoreData => nextUrl != null;

  @override
  void dispose() {
    isClosed = true;
    activeTripsToken?.cancel();
    refreshController.dispose();
    activeTrips.clear();
    isLoading.dispose();
    super.dispose();
  }
}
