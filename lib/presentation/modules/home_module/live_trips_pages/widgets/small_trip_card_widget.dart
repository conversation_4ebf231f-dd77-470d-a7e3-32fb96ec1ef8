// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/create_trip_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';
import 'package:transportmatch_provider/widgets/location_widget.dart';

class SmallTripCard extends StatelessWidget {
  const SmallTripCard({
    required this.data,
    super.key,
  });
  final SavedRoute data;

  /// Handle edit trip navigation based on trip type
  void _handleEditTrip(BuildContext context, SavedRoute tripData) {
    // Check if trip is exclusive or shared
    final isExclusiveTrip =
        tripData.tripType?.toUpperCase() == TripType.EXCLUSIVE.name ||
            tripData.exclusiveTripData != null;

    if (isExclusiveTrip) {
      // Navigate to exclusive trip edit page
      _navigateToExclusiveTripEdit(context, tripData);
    } else {
      // Navigate to shared trip edit page (create trip page)
      _navigateToSharedTripEdit(context, tripData);
    }
  }

  /// Navigate to exclusive trip edit page
  void _navigateToExclusiveTripEdit(
    BuildContext context,
    SavedRoute tripData,
  ) {
    // Create providers needed for exclusive trip edit
    final tripDataProvider = TripDataProvider();
    final requestedTripProvider = RequestedTripProvider();

    // Convert AcceptedTripModelData to ExclusiveTrip
    final exclusiveTrip = ExclusiveTrip(
      id: tripData.id,
      userStartLocation: UserLocation(
        street: tripData.exclusiveTripData?.userStartLocation?.street,
        neighborhood:
            tripData.exclusiveTripData?.userStartLocation?.neighborhood,
        city: tripData.exclusiveTripData?.userStartLocation?.city,
        state: tripData.exclusiveTripData?.userStartLocation?.state,
        postalCode: tripData.exclusiveTripData?.userStartLocation?.postalCode,
        country: tripData.exclusiveTripData?.userStartLocation?.country,
        countryCode: tripData.exclusiveTripData?.userStartLocation?.countryCode,
        latitude: tripData.exclusiveTripData?.userStartLocation?.latitude,
        longitude: tripData.exclusiveTripData?.userStartLocation?.longitude,
      ),
      userEndLocation: UserLocation(
        street: tripData.exclusiveTripData?.userEndLocation?.street,
        neighborhood: tripData.exclusiveTripData?.userEndLocation?.neighborhood,
        city: tripData.exclusiveTripData?.userEndLocation?.city,
        state: tripData.exclusiveTripData?.userEndLocation?.state,
        postalCode: tripData.exclusiveTripData?.userEndLocation?.postalCode,
        country: tripData.exclusiveTripData?.userEndLocation?.country,
        countryCode: tripData.exclusiveTripData?.userEndLocation?.countryCode,
        latitude: tripData.exclusiveTripData?.userEndLocation?.latitude,
        longitude: tripData.exclusiveTripData?.userEndLocation?.longitude,
      ),
      customerStartDate: tripData.tripStartDate,
      customerEndDate: tripData.tripEndDate,
      totalTripDistance: tripData.totalTripDistance,
    );

    data.toJson().logD;
    exclusiveTrip.toJson().logE;

    // Navigate to exclusive trip offer page in edit mode
    AppNavigationService.pushNamed(
      context,
      AppRoutes.tripsExclusiveRequestedScreen,
      extra: ExclusiveRequestedParams(
        // tripId: tripData.id,
        exclusiveBooking: exclusiveTrip,
        requestedTripProvider: requestedTripProvider,
        tripDataProvider: tripDataProvider,
        isEditMode: true,
        existingOfferId: tripData.exclusiveTripData?.exclusiveTripId ??
            tripData.id?.toString(),
        existingEquipmentId: tripData.equipment?.id,
        existingDriverId: tripData.driver?.id,
        existingSpotAvailable: tripData.spotAvailableForReservation,
        existingCostPerKilometer: tripData.costPerKilometer,
        existingTotalTripDistance: tripData.totalTripDistance,
      ),
    );
  }

  /// Navigate to shared trip edit page (create trip page)
  void _navigateToSharedTripEdit(
    BuildContext context,
    SavedRoute tripData,
  ) {
    AppNavigationService.pushNamed(
      context,
      AppRoutes.homeNewRouteScreen,
      extra: CreateTripParams(
        savedRouteData: data,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isExclusiveTrip =
        data.tripType?.toUpperCase() == TripType.EXCLUSIVE.name ||
            data.exclusiveTripData != null;
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.sp16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r6),
        color: AppColors.white,
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LocationWidget(
              title1: isExclusiveTrip
                  ? data.exclusiveTripData?.userStartLocation?.street ?? ''
                  : data.startStopLocation?.fullAddress ?? 'Start',
              date1: data.tripStartDate?.monthDate ?? '',
              title2: isExclusiveTrip
                  ? data.exclusiveTripData?.userEndLocation?.street ?? ''
                  : data.endStopLocation?.fullAddress ?? 'End',
              date2: data.tripEndDate?.monthDate ?? '',
              startLatitude: isExclusiveTrip
                  ? data.exclusiveTripData?.userStartLocation?.latitude ?? ''
                  : data.startStopLocation?.latitude ?? '',
              startLongitude: isExclusiveTrip
                  ? data.exclusiveTripData?.userStartLocation?.longitude ?? ''
                  : data.startStopLocation?.longitude ?? '',
              endLatitude: isExclusiveTrip
                  ? data.exclusiveTripData?.userEndLocation?.latitude ?? ''
                  : data.endStopLocation?.latitude ?? '',
              endLongitude: isExclusiveTrip
                  ? data.exclusiveTripData?.userEndLocation?.longitude ?? ''
                  : data.endStopLocation?.longitude ?? '',
            ),
            Gap(AppSize.h14),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: AppSize.h4,
                    children: [
                      Text(
                        context.l10n.driver,
                        style: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp12,
                          color: AppColors.ff6C757D,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        "${data.driver?.firstName ?? ''} ${data.driver?.lastName ?? ''}"
                            .trim(),
                        style: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp16,
                          color: AppColors.ff343A40,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                TextButton(
                  onPressed: () => _handleEditTrip(context, data),
                  child: Text(
                    context.l10n.edit,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontSize: AppSize.sp16,
                      color: AppColors.ff0087C7,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
