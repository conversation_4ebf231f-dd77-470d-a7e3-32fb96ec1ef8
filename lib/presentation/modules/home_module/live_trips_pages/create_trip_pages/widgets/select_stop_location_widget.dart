// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

class SelectStopLocationWidget extends StatelessWidget {
  const SelectStopLocationWidget({
    required this.createTripProvider,
    super.key,
  });

  final CreateTripProvider createTripProvider;
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: createTripProvider.allowIntermediatePickup,
      builder: (context, value, _) {
        return value && createTripProvider.stopCities.value.isNotEmpty
            ? Opacity(
                opacity: createTripProvider.isEdit ? 0.5 : 1,
                child: AbsorbPointer(
                  absorbing: createTripProvider.isEdit,
                  child: ValueListenableBuilder(
                    valueListenable: createTripProvider.selectedStopCities,
                    builder: (context, value, child) {
                      final length = createTripProvider.stopCities.value.length;
                      return length == 0
                          ? const SizedBox()
                          : Container(
                              padding: EdgeInsets.all(AppSize.sp18),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(AppSize.r6),
                                color: AppColors.white,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    context.l10n.stopsAtMajorCities,
                                    style: context.textTheme.displayMedium
                                        ?.copyWith(
                                      fontSize: AppSize.sp20,
                                      fontWeight: FontWeight.w700,
                                      color: AppColors.ff212529,
                                    ),
                                  ),
                                  Gap(AppSize.h2),
                                  Text(
                                    context
                                        .l10n.collectionAndDeliveryDescription,
                                    style:
                                        context.textTheme.bodyMedium?.copyWith(
                                      fontSize: AppSize.sp12,
                                      fontWeight: FontWeight.w500,
                                      color: AppColors.ff6C757D,
                                    ),
                                  ),
                                  Gap(AppSize.h20),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: ListView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount: length - (length ~/ 2),
                                          itemBuilder: (context, index) {
                                            final data = createTripProvider
                                                .stopCities.value[index * 2];
                                            return ValueListenableBuilder(
                                              valueListenable:
                                                  createTripProvider
                                                      .selectedStopCities,
                                              builder: (context, value, child) {
                                                final isSelected = value.any(
                                                  (e) => e.id == data.id,
                                                );
                                                return Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Checkbox.adaptive(
                                                      value: isSelected,
                                                      onChanged: (val) =>
                                                          createTripProvider
                                                              .addAndRemoveStopCitiesOrCustomWayPoints(
                                                        data,
                                                        isSelected: isSelected,
                                                      ),
                                                      checkColor:
                                                          AppColors.white,
                                                      fillColor:
                                                          WidgetStateProperty
                                                              .all(
                                                        isSelected
                                                            ? AppColors
                                                                .primaryColor
                                                            : Colors
                                                                .transparent,
                                                      ),
                                                    ),
                                                    Flexible(
                                                      child: Text(
                                                        ' ${data.name}',
                                                        maxLines: 1,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: context.textTheme
                                                            .bodyMedium
                                                            ?.copyWith(
                                                          fontWeight:
                                                              FontWeight.w400,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                        ),
                                      ),
                                      Container(
                                        height: (length - (length ~/ 2)) * 48,
                                        width: 2,
                                        padding: EdgeInsets.only(
                                          right: AppSize.sp10,
                                        ),
                                        child: const VerticalDivider(
                                          color: AppColors.ffCED4DA,
                                          thickness: 1,
                                        ),
                                      ),
                                      Expanded(
                                        child: ListView.builder(
                                          shrinkWrap: true,
                                          padding: EdgeInsets.only(
                                            left: AppSize.sp10,
                                          ),
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount: length ~/ 2,
                                          itemBuilder: (context, index) {
                                            final data = createTripProvider
                                                .stopCities
                                                .value[(index * 2) + 1];
                                            return ValueListenableBuilder(
                                              valueListenable:
                                                  createTripProvider
                                                      .selectedStopCities,
                                              builder: (context, value, child) {
                                                final isSelected = value.any(
                                                  (e) => e.id == data.id,
                                                );
                                                return Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Checkbox.adaptive(
                                                      value: isSelected,
                                                      onChanged: (val) =>
                                                          createTripProvider
                                                              .addAndRemoveStopCitiesOrCustomWayPoints(
                                                        data,
                                                        isSelected: isSelected,
                                                      ),
                                                      checkColor:
                                                          AppColors.white,
                                                      fillColor:
                                                          WidgetStateProperty
                                                              .all(
                                                        isSelected
                                                            ? AppColors
                                                                .primaryColor
                                                            : Colors
                                                                .transparent,
                                                      ),
                                                    ),
                                                    Flexible(
                                                      child: Text(
                                                        ' ${data.name}',
                                                        maxLines: 1,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: context.textTheme
                                                            .bodyMedium
                                                            ?.copyWith(
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          fontSize:
                                                              AppSize.sp14,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                    },
                  ),
                ),
              )
            : value && createTripProvider.stopCities.value.isEmpty
                ? Container(
                    padding: EdgeInsets.all(AppSize.sp18),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppSize.r6),
                      color: AppColors.white,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(
                          context.l10n.stopsAtMajorCities,
                          style: context.textTheme.displayMedium?.copyWith(
                            fontSize: AppSize.sp20,
                            fontWeight: FontWeight.w700,
                            color: AppColors.ff212529,
                          ),
                        ),
                        Gap(AppSize.h2),
                        Text(
                          context.l10n.stopLocationsNotAvailableForThisRoute,
                          style: context.textTheme.bodyMedium?.copyWith(
                            fontSize: AppSize.sp12,
                            fontWeight: FontWeight.w500,
                            color: AppColors.ff6C757D,
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink();
      },
    );
  }
}
