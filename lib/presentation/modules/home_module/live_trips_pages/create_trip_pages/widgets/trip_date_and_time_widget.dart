// ignore_for_file: public_member_api_docs, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/app_image.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';

class TripDateAndTimeWidget extends StatelessWidget {
  const TripDateAndTimeWidget({
    required this.createTripProvider,
    super.key,
  });

  final CreateTripProvider createTripProvider;
  @override
  Widget build(BuildContext context) {
    final endDate = DateTime.now().add(const Duration(days: 60));
    final afterTwo = DateTime.now().add(const Duration(days: 3));
    final selectedIntermediatePoints =
        createTripProvider.selectedStopCities.value.where((e) {
      return e.id != null;
    }).toList();
    return Opacity(
      opacity: createTripProvider.isEdit ? 0.5 : 1,
      child: AbsorbPointer(
        absorbing: createTripProvider.isEdit,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(AppSize.sp18),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppSize.r6),
                color: AppColors.white,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.l10n.tripStartAndEndDates,
                    style: context.textTheme.displayMedium?.copyWith(
                      fontSize: AppSize.sp20,
                      fontWeight: FontWeight.w700,
                      color: AppColors.ff212529,
                    ),
                  ),
                  Gap(AppSize.sp10),
                  AppTextFormField(
                    title: context.l10n.startDate, // Localized string
                    readOnly: true,
                    hintText: 'DD/MM/YYYY',
                    controller: TextEditingController(
                      text: createTripProvider.tripeStartDate.value?.dateOnly,
                    ),
                    fillColor: AppColors.ffF8F9FA,
                    onTap: () {
                      final isAfter = createTripProvider.tripeStartDate.value
                              ?.isAfter(afterTwo) ??
                          false;
                      showDatePicker(
                        context: context,
                        initialDate: (isAfter
                                ? createTripProvider.tripeStartDate.value
                                : afterTwo) ??
                            afterTwo,
                        firstDate: afterTwo,
                        lastDate: endDate,
                      ).then((pickedDate) {
                        if (pickedDate != null) {
                          for (var i = 0;
                              i < selectedIntermediatePoints.length;
                              i++) {
                            selectedIntermediatePoints[i].date = null;
                            selectedIntermediatePoints[i].time = null;
                          }
                          createTripProvider
                            ..tripeStartDate.value = pickedDate
                            ..tripeEndDate.value = null
                            ..selectedDriver.clearDropDown()
                            ..selectedEquipment.clearDropDown()
                            ..availableSlot.clear()
                            ..notify();
                        }
                        //do whatever you want
                      });
                    },
                    suffixIcon: AppImage.asset(AppAssets.iconsCalender.path),
                  ),
                  Gap(AppSize.sp10),
                  AppTextFormField(
                    title: context.l10n.endDate, // Localized string
                    readOnly: true,
                    hintText: 'DD/MM/YYYY',
                    fillColor: AppColors.ffF8F9FA,
                    controller: TextEditingController(
                      text: createTripProvider.tripeEndDate.value?.dateOnly,
                    ),
                    onTap: () {
                      final startDate = createTripProvider.tripeStartDate.value;
                      final firstDateForEnd = startDate != null ? startDate.add(
                          const Duration(days: 1),) : afterTwo;

                      showDatePicker(
                        context: context,
                        initialDate: createTripProvider.tripeEndDate.value ??
                            firstDateForEnd,
                        firstDate: firstDateForEnd,
                        lastDate: endDate,
                      ).then((pickedDate) {
                        if (pickedDate != null) {
                          for (var i = 0;
                          i <
                              selectedIntermediatePoints.length; i++) {
                            selectedIntermediatePoints[i].date = null;
                            selectedIntermediatePoints[i].time = null;
                          }
                          createTripProvider
                            ..tripeEndDate.value = pickedDate
                            ..selectedDriver.clearDropDown()
                            ..selectedEquipment.clearDropDown()
                            ..availableSlot.clear()
                            ..getDropDownListApiCall(
                              startDate: createTripProvider.tripeStartDate.value
                                  ?.dateDropDownApiPramOnly ??
                                  '',
                              endDate: createTripProvider.tripeEndDate.value
                                  ?.dateDropDownApiPramOnly ??
                                  '',
                            )
                            ..notify();
                        }
                      });
                    },
                    suffixIcon: AppImage.asset(AppAssets.iconsCalender.path),
                  ),
                ],
              ),
            ),
            if (selectedIntermediatePoints.isNotEmpty)
              Container(
                padding: EdgeInsets.all(AppSize.sp18),
                margin: EdgeInsets.only(top: AppSize.sp20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppSize.r6),
                  color: AppColors.white,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.l10n.intermediateStopsDates,
                      style: context.textTheme.displayMedium?.copyWith(
                        fontSize: AppSize.sp20,
                        fontWeight: FontWeight.w700,
                        color: AppColors.ff212529,
                      ),
                    ),
                    Gap(AppSize.sp10),
                    ListView.builder(
                      itemCount: selectedIntermediatePoints.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      itemBuilder: (context, index) {
                        final data = selectedIntermediatePoints[index];
                        return AppPadding.symmetric(
                          vertical: AppSize.h8,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${context.l10n.dateAndTimeFor} ${data.name}',
                                style: context.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Gap(AppSize.sp8),
                              Row(
                                children: [
                                  Flexible(
                                    child: AppTextFormField(
                                      readOnly: true,
                                      hintText: 'DD/MM/YYYY',
                                      controller: TextEditingController(
                                        text: data.date?.dateOnly,
                                      ),
                                      fillColor: AppColors.ffF8F9FA,
                                      onTap: () {
                                        if (createTripProvider
                                                    .tripeStartDate.value !=
                                                null &&
                                            createTripProvider
                                                    .tripeEndDate.value !=
                                                null) {
                                          if (index != 0) {
                                            if (selectedIntermediatePoints[
                                                            index - 1]
                                                        .date ==
                                                    null ||
                                                selectedIntermediatePoints[
                                                            index - 1]
                                                        .time ==
                                                    null) {
                                              'Please select estimated date and time for ${selectedIntermediatePoints[index - 1].name}'
                                                  .showErrorAlert();
                                              return;
                                            }
                                          }
                                          showDatePicker(
                                            context: context,
                                            initialDate: data.date ??
                                                (index == 0
                                                    ? createTripProvider
                                                        .tripeStartDate.value
                                                    : selectedIntermediatePoints[
                                                            index - 1]
                                                        .date) ??
                                                createTripProvider
                                                    .tripeStartDate.value,
                                            firstDate: (index == 0
                                                    ? createTripProvider
                                                        .tripeStartDate.value
                                                    : selectedIntermediatePoints[
                                                            index - 1]
                                                        .date) ??
                                                afterTwo,
                                            lastDate: createTripProvider
                                                    .tripeEndDate.value ??
                                                endDate,
                                          ).then((pickedDate) {
                                            if (pickedDate != null) {
                                              final selectedCityIndex =
                                                  createTripProvider
                                                      .selectedStopCities.value
                                                      .indexWhere((e) =>
                                                          e.id == data.id,);
                                              if (selectedCityIndex == -1) {
                                                return;
                                              }
                                              createTripProvider
                                                ..selectedStopCities
                                                    .value[selectedCityIndex]
                                                    .date = pickedDate
                                                ..notify();
                                            }
                                          });
                                        } else {
                                          context.l10n.selectStartEndDate
                                              .showErrorAlert();
                                        }
                                      },
                                      suffixIcon: AppImage.asset(
                                        AppAssets.iconsCalender.path,
                                      ),
                                    ),
                                  ),
                                  Gap(AppSize.w6),
                                  Flexible(
                                    child: AppTextFormField(
                                      readOnly: true,
                                      hintText: '00:00',
                                      controller: TextEditingController(
                                        text: data.time?.format(context),
                                      ),
                                      fillColor: AppColors.ffF8F9FA,
                                      onTap: () {
                                        if (createTripProvider
                                                    .tripeStartDate.value !=
                                                null &&
                                            createTripProvider
                                                    .tripeEndDate.value !=
                                                null) {
                                          DateTime? preDate;
                                          if (index != 0) {
                                            if (selectedIntermediatePoints[
                                                            index - 1]
                                                        .date !=
                                                    null &&
                                                selectedIntermediatePoints[
                                                            index - 1]
                                                        .time !=
                                                    null) {
                                              preDate =
                                                  selectedIntermediatePoints[
                                                          index - 1]
                                                      .date
                                                      ?.setTimeOfDay(
                                                        selectedIntermediatePoints[
                                                                index - 1]
                                                            .time!,
                                                      );
                                            } else {
                                              'Please select estimated date and time for ${selectedIntermediatePoints[index - 1].name}'
                                                  .showErrorAlert();
                                              return;
                                            }
                                          }
                                          if (selectedIntermediatePoints[index]
                                                  .date !=
                                              null) {
                                            showTimePicker(
                                              context: context,
                                              initialTime: const TimeOfDay(
                                                hour: 0,
                                                minute: 0,
                                              ),
                                            ).then((value) {
                                              if (value != null) {
                                                final newDate =
                                                    selectedIntermediatePoints[
                                                            index]
                                                        .date
                                                        ?.setTimeOfDay(
                                                          value,
                                                        );

                                                if (index == 0 ||
                                                    newDate!
                                                        .isAfter(preDate!)) {
                                                  final selectedCityIndex =
                                                      createTripProvider
                                                          .selectedStopCities
                                                          .value
                                                          .indexWhere((e) =>
                                                              e.id == data.id,);
                                                  if (selectedCityIndex == -1) {
                                                    return;
                                                  }
                                                  createTripProvider
                                                    ..selectedStopCities
                                                        .value[
                                                            selectedCityIndex]
                                                        .time = value
                                                    ..notify();
                                                } else {
                                                  'Please select valid time'
                                                      .showErrorAlert();
                                                }
                                              }
                                            });
                                          } else {
                                            'Please select date first'
                                                .showErrorAlert();
                                          }
                                        } else {
                                          context.l10n.selectStartEndDate
                                              .showErrorAlert();
                                        }
                                      },
                                      suffixIcon: AppImage.asset(
                                        AppAssets.iconsTime.path,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
