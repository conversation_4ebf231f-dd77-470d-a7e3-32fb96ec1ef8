// ignore_for_file: public_member_api_docs

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/equipment_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_dropdown.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';

class TripInfoWidget extends StatelessWidget {
  const TripInfoWidget({
    required this.createTripProvider,
    super.key,
  });

  final CreateTripProvider createTripProvider;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSize.sp18),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r6),
        color: AppColors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.l10n.tripInfo,
            style: context.textTheme.displayMedium?.copyWith(
              fontSize: AppSize.sp20,
              fontWeight: FontWeight.w700,
              color: AppColors.ff212529,
            ),
          ),
          Gap(AppSize.sp10),
          ValueListenableBuilder(
            valueListenable: createTripProvider.equipmentListModelList,
            builder: (context, equipmentList, _) {
              return InkWell(
                onTap: () {
                  if (createTripProvider.tripeStartDate.value == null ||
                      createTripProvider.tripeEndDate.value == null) {
                    context.l10n.pleaseSelectedStartAndEndDate.showInfoAlert();
                  } else if (equipmentList.isEmpty) {
                    context.l10n.noEquipmentAvailableOnSelectedDate
                        .showInfoAlert();
                  }
                },
                child: AbsorbPointer(
                  absorbing: createTripProvider.tripeStartDate.value == null ||
                      createTripProvider.tripeEndDate.value == null ||
                      equipmentList.isEmpty,
                  child: AppDropdown(
                    title: context.l10n.selectEquipment,
                    hintText: context.l10n.chooseEquipment,
                    fillColor: AppColors.ffF8F9FA,
                    controller: createTripProvider.selectedEquipment,

                    onChanged: (dropdownValue) {
                      if (dropdownValue != null &&
                          dropdownValue is DropDownValueModel) {
                        createTripProvider.selectedEquipment.setDropDown(
                          dropdownValue,
                          // DropDownValueModel(
                          //   value: value,
                          //   name: (value as EquipmentDataModel).name ?? ' ',
                          // ),
                        );
                        // controller.selectedEquipment = controller.equipDataList
                        //     .firstWhere((element) => element.id.toString() == value);
                        createTripProvider.availableSlot.text =
                            (dropdownValue.value as EquipmentDropDownModel)
                                .slot
                                .toString();
                        createTripProvider.notify();
                        // controller
                        //   ..selectedEquipment?.slot.logE
                        //   ..notify();
                      }
                    },
                    // selectedItem: controller.selectedEquipment?.id.toString(),
                    items: equipmentList
                        .map(
                          (e) => DropDownValueModel(
                            value: e,
                            name: e.name ?? '',
                          ),
                        )
                        .toList(),
                  ),
                ),
              );
            },
          ),
          Gap(AppSize.sp20),
          Selector<CreateTripProvider, EquipmentDropDownModel?>(
            selector: (p0, p1) => p1.selectedEquipment.dropDownValue?.value
                as EquipmentDropDownModel?,
            builder: (context, value, child) {
              return AppTextFormField(
                title: context.l10n.enterAvailableSlots,
                fillColor: AppColors.ffF8F9FA,
                keyboardType: TextInputType.number,
                controller: createTripProvider.availableSlot,
                maxTextLength: value?.slot.toString().length,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  TextInputFormatter.withFunction((oldValue, newValue) {
                    final text = newValue.text;
                    if (text == '0' ||
                        (text.length > 1 && text.startsWith('0'))) {
                      return oldValue;
                    }
                    return newValue;
                  }),
                ],
                onChanged: (p0) {
                  //calculate total cost if distance is greater than 0 and available slot is greater than 0 and km per is greater than 0
                  if (createTripProvider.distance.value > 0 &&
                      createTripProvider.availableSlot.text.isNotEmpty &&
                      createTripProvider.kmPerController.text.isNotEmpty) {
                    final costPerKm = double.tryParse(
                        createTripProvider.kmPerController.text,);
                    if (costPerKm != null) {
                      final distanceInKm =
                          createTripProvider.distance.value / 1000;
                      // Calculate total cost based on cost per km and distance
                      final totalCost = costPerKm * distanceInKm;
                      createTripProvider.totalTripController.text = (totalCost *
                              (int.tryParse(
                                    createTripProvider.availableSlot.text,
                                  ) ??
                                  1))
                          .toStringAsFixed(2); // Keep 2 decimal places
                    }
                  } else {
                    createTripProvider.totalTripController.clear();
                    createTripProvider.kmPerController.clear();
                  }
                },
                readOnly: createTripProvider.tripeStartDate.value == null ||
                    createTripProvider.tripeEndDate.value == null ||
                    createTripProvider.selectedEquipment.dropDownValue == null,
                onTap: () {
                  if (createTripProvider.tripeStartDate.value == null ||
                      createTripProvider.tripeEndDate.value == null) {
                    context.l10n.pleaseSelectedStartAndEndDate.showInfoAlert();
                  } else if (createTripProvider
                          .selectedEquipment.dropDownValue ==
                      null) {
                    context.l10n.pleaseSelectEquipment.showInfoAlert();
                  }
                },
              );
            },
          ),
          Gap(AppSize.sp20),
          ValueListenableBuilder(
            valueListenable: createTripProvider.driverListModelList,
            builder: (context, driverList, _) {
              return InkWell(
                onTap: () {
                  if (createTripProvider.tripeStartDate.value == null ||
                      createTripProvider.tripeEndDate.value == null) {
                    context.l10n.pleaseSelectedStartAndEndDate.showInfoAlert();
                  } else if (driverList.isEmpty) {
                    context.l10n.noDriversAvailableOnSelectedDate
                        .showInfoAlert();
                  }
                },
                child: AbsorbPointer(
                  absorbing: createTripProvider.tripeStartDate.value == null ||
                      createTripProvider.tripeEndDate.value == null ||
                      driverList.isEmpty,
                  child: AppDropdown(
                    title: context.l10n.selectDriver,
                    controller: createTripProvider.selectedDriver,
                    fillColor: AppColors.ffF8F9FA,
                    hintText: context.l10n.chooseDriver,
                    // onChanged: (value) {
                    //   if (value != null) {
                    //     controller.selectedDriver =
                    //         controller.driverDataList.firstWhere(
                    //       (element) =>
                    //           '${element.firstName} ${element.lastName}'.nullString ==
                    //           value,
                    //     );
                    //   }
                    // },
                    // selectedItem:
                    //     '${controller.selectedDriver?.firstName ?? ''} ${controller.selectedDriver?.lastName ?? ''}'
                    // .nullString,
                    items: driverList
                        .map(
                          (e) => DropDownValueModel(
                            value: e,
                            name: '${e.firstName ?? ''} ${e.lastName ?? ''}'
                                .trim(),
                          ),
                        )
                        .toList(),
                  ),
                ),
              );
            },
          ),
          Gap(AppSize.sp4),
          if (createTripProvider.isEdit)
            Text(
              context.l10n.changeDriverNote,
              style: context.textTheme.bodyMedium?.copyWith(
                color: AppColors.ff343A40,
                fontSize: AppSize.sp10,
              ),
            ),
        ],
      ),
    );
  }
}
