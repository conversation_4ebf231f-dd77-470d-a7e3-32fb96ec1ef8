// ignore_for_file: public_member_api_docs

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/create_trip_params.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/provider/live_trips_provider.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/widgets/small_trip_card_widget.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class LiveTripsPage extends StatelessWidget {
  const LiveTripsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => LiveTripsProvider(),
      child: Consumer<LiveTripsProvider>(
        builder: (context, liveTripsProvider, _) {
          return Scaffold(
            backgroundColor: AppColors.pageBGColor,
            appBar: CustomAppBar(
              title: context.l10n.allLiveTrips,
              actions: [
                InkWell(
                  onTap: () async {
                    await AppNavigationService.pushNamed(
                      context,
                      AppRoutes.homeNewRouteScreen,
                      extra: CreateTripParams(),
                      afterBack: (value) {
                        if (value != null && value is bool) {
                          if (value) {
                            liveTripsProvider.getTripDataApiCall(
                              isWantShowLoader: true,
                            );
                          }
                        }
                      },
                    );
                  },
                  child: Row(
                    children: [
                      const Icon(
                        Icons.add,
                        color: AppColors.primaryColor,
                      ),
                      Text(
                        context.l10n.newRoute,
                        style: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp16,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            body: AppPadding.symmetric(
              horizontal: AppSize.appPadding,
              child: ValueListenableBuilder(
                valueListenable: liveTripsProvider.isShowLoader,
                builder: (context, value, child) {
                  return AppLoader(isShowLoader: value, child: child!);
                },
                child: EasyRefresh(
                  header: AppCommonFunctions.getLoadingHeader(),
                  footer: AppCommonFunctions.getLoadingFooter(),
                  controller: liveTripsProvider.refreshController,
                  onRefresh: () => liveTripsProvider.getTripDataApiCall(),
                  onLoad: liveTripsProvider.nextUrl.value.isNotEmptyAndNotNull
                      ? () => liveTripsProvider.getTripDataApiCall(
                            url: liveTripsProvider.nextUrl.value,
                          )
                      : null,
                  child: ValueListenableBuilder(
                    valueListenable: liveTripsProvider.liveTripList,
                    builder: (context, list, child) {
                      return ValueListenableBuilder(
                        valueListenable: liveTripsProvider.isShowLoader,
                        builder: (context, value, child) {
                          return list.isEmpty && !value
                              ? ListView(
                                  children: [
                                    SizedBox(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.7,
                                      child: Center(
                                        child: Text(
                                          context.l10n.noTripLiveYet,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : value
                                  ? const SizedBox.expand()
                                  : child!;
                        },
                        child: ListView.builder(
                          // shrinkWrap: true,
                          // physics: const NeverScrollableScrollPhysics(),
                          // shrinkWrap: true,
                          // physics: const NeverScrollableScrollPhysics(),
                          itemCount: list.length,
                          padding: EdgeInsets.zero,
                          itemBuilder: (context, index) {
                            return SmallTripCard(data: list[index]);
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
