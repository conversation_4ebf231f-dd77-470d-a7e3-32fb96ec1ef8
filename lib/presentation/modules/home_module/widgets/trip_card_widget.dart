// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/create_trip_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_image.dart';
import 'package:transportmatch_provider/widgets/gap.dart';
import 'package:transportmatch_provider/widgets/location_widget.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

class TripCard extends StatelessWidget {
  const TripCard({
    required this.tripData,
    this.onTap,
    super.key,
  });

  final AcceptedTripModelData tripData;
  final VoidCallback? onTap;

  /// Handle edit trip navigation based on trip type
  void _handleEditTrip(BuildContext context, AcceptedTripModelData tripData) {
    // Check if trip is exclusive or shared
    final isExclusiveTrip =
        tripData.tripType?.toUpperCase() == TripType.EXCLUSIVE.name ||
            tripData.exclusiveTrips != null;

    if (isExclusiveTrip) {
      // Navigate to exclusive trip edit page
      _navigateToExclusiveTripEdit(context, tripData);
    } else {
      // Navigate to shared trip edit page (create trip page)
      _navigateToSharedTripEdit(context, tripData);
    }
  }

  /// Navigate to exclusive trip edit page
  void _navigateToExclusiveTripEdit(
    BuildContext context,
    AcceptedTripModelData tripData,
  ) {
    // Create providers needed for exclusive trip edit
    final tripDataProvider = TripDataProvider();
    final requestedTripProvider = RequestedTripProvider();

    // Convert AcceptedTripModelData to ExclusiveTrip
    final exclusiveTrip = ExclusiveTrip(
      id: tripData.id,
      userStartLocation: tripData.exclusiveTrips?.userStartLocation,
      userEndLocation: tripData.exclusiveTrips?.userEndLocation,
      customerStartDate: tripData.tripStartDate,
      customerEndDate: tripData.tripEndDate,
      totalTripDistance: tripData.totalTripDistance,
    );

    // Navigate to exclusive trip offer page in edit mode
    AppNavigationService.pushNamed(
      context,
      AppRoutes.tripsExclusiveRequestedScreen,
      extra: ExclusiveRequestedParams(
        // tripId: tripData.id,
        exclusiveBooking: exclusiveTrip,
        requestedTripProvider: requestedTripProvider,
        tripDataProvider: tripDataProvider,
        isEditMode: true,
        existingOfferId:
            tripData.exclusiveTrips?.exclusiveTripId ?? tripData.tripId ?? '',
        existingEquipmentId: tripData.equipment?.id,
        existingDriverId: tripData.driver?.id,
        existingSpotAvailable: tripData.spotAvailableForReservation?.toInt(),
        existingCostPerKilometer: tripData.costPerKilometer,
        existingTotalTripDistance: tripData.totalTripDistance,
      ),
    );
  }

  /// Navigate to shared trip edit page (create trip page)
  void _navigateToSharedTripEdit(
    BuildContext context,
    AcceptedTripModelData tripData,
  ) {
    AppNavigationService.pushNamed(
      context,
      AppRoutes.homeNewRouteScreen,
      extra: CreateTripParams(
        tripId: tripData.id?.toString(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final totalCost =
        ((tripData.totalTripDistance ?? 0) * (tripData.costPerKilometer ?? 0))
            .round();
    final perCarCost =
        tripData.totalBookedSlots != null && tripData.totalBookedSlots! > 0
            ? (totalCost / tripData.totalBookedSlots!).round()
            : 0;

    final isExclusiveTrip =
        tripData.tripType?.toUpperCase() == TripType.EXCLUSIVE.name ||
            tripData.exclusiveTrips != null;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: AppSize.sp16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSize.r6),
          color: AppColors.white,
        ),
        padding: EdgeInsets.all(AppSize.sp16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: AppSize.h16,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppImage.asset(
                      AppAssets.iconsCarHaulerTruck.path,
                      color: AppColors.primaryColor,
                      height: AppSize.sp32,
                      width: AppSize.sp32,
                    ),
                    Gap(AppSize.h4),
                    Text(
                      tripData.equipment?.name ?? ' - ',
                      style: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                TitleInfoWidget(
                  title: l10n.availableSlot,
                  spacing: AppSize.h4,
                  subTitle:
                      '${tripData.totalBookedSlots ?? 0}/${tripData.spotAvailableForReservation ?? 0}',
                ),
                TextButton(
                  onPressed: () => _handleEditTrip(context, tripData),
                  child: Text(
                    context.l10n.edit,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontSize: AppSize.sp16,
                      color: AppColors.ff0087C7,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TitleInfoWidget(
                  title: l10n.driver,
                  spacing: AppSize.h4,
                  subTitle: tripData.driver?.firstName ?? ' - ',
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      context.l10n.totalTripCost,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp12,
                        color: AppColors.ffADB5BD,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      context.l10n
                          .tripCost((totalCost).toString().smartFormat()),
                      style: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.ff67509C,
                      ),
                    ),
                    if (perCarCost != 0)
                      Text(
                        context.l10n
                            .perCarCost(perCarCost.toString().smartFormat()),
                        style: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp12,
                          color: AppColors.ffADB5BD,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ],
            ),
            LocationWidget(
              title1: isExclusiveTrip
                  ? tripData.exclusiveTrips?.userStartLocation?.street ?? ''
                  : tripData.startStopLocation?.name ?? '',
              date1: tripData.tripStartDate?.monthDate ?? '',
              title2: isExclusiveTrip
                  ? tripData.exclusiveTrips?.userEndLocation?.street ?? ''
                  : tripData.endStopLocation?.name ?? '',
              date2: tripData.tripEndDate?.monthDate ?? '',
              startLatitude: tripData.startStopLocation?.address?.latitude ??
                  tripData.exclusiveTrips?.userStartLocation?.latitude ??
                  '',
              startLongitude: tripData.startStopLocation?.address?.longitude ??
                  tripData.exclusiveTrips?.userStartLocation?.longitude ??
                  '',
              endLatitude: tripData.endStopLocation?.address?.latitude ??
                  tripData.exclusiveTrips?.userEndLocation?.latitude ??
                  '',
              endLongitude: tripData.endStopLocation?.address?.longitude ??
                  tripData.exclusiveTrips?.userEndLocation?.longitude ??
                  '',
            ),
          ],
        ),
      ),
    );
  }
}
