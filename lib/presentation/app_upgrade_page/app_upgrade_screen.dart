import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/app_upgrade_page/models/app_upgrade_params.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:url_launcher/url_launcher.dart';

class AppUpdateScreen extends StatelessWidget {
  const AppUpdateScreen({
    super.key,
    required this.appUpgradeParams,
  });

  final AppUpgradeParams  appUpgradeParams;


  Future<void> _launchStore() async {
    // https://play.google.com/store/apps/details?id=com.face26Mobile.app - play store url
    // https://apps.apple.com/app/face26/id1658951083 - app store url
    /// TODO : when app will deploy then update url of play store and app store
    // final url = Platform.isAndroid
    //     ? ''
    //     : '';

    const url = 'https://www.google.com/';

    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Could not launch store URL';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Positioned(
            bottom: -25,
            child: Image.asset(
              'assets/images/upgrade_logo.png',
              height: MediaQuery.sizeOf(context).height,
              width: MediaQuery.sizeOf(context).width,
              fit: BoxFit.fill,
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              height: MediaQuery.sizeOf(context).height * 0.28,
              alignment: Alignment.center,
              child: Text(
                context.l10n.newUpdateAvailable,
                style: context.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.white,
                  fontSize: AppSize.sp26,
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: MediaQuery.sizeOf(context).height * .3,
              width: MediaQuery.sizeOf(context).width,
              alignment: Alignment.center,
              padding:  EdgeInsets.symmetric(horizontal: AppSize.w16, vertical: AppSize.h8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                spacing: AppSize.sp12,
                children: [
                  Flexible(
                    child: Text(
                        context.l10n.updateMessage,
                        textAlign: TextAlign.center,
                        style: context.textTheme.bodyMedium?.copyWith(
                            fontSize: AppSize.sp16, color: Colors.black87,
                        ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(AppSize.r8),
                    ),
                    padding: EdgeInsets.all(AppSize.sp12),
                    child: Text(
                      context.l10n.versionInfo(appUpgradeParams.currentAppVersion, appUpgradeParams.minimumAppVersion),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.symmetric(horizontal: AppSize.w16,vertical: AppSize.h20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            AppButton(
              text: context.l10n.updateNow,
              textStyle:context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.white,
                fontSize: AppSize.sp16,
              ) ,
              backgroundColor: AppColors.secondaryColor,
              icon: const Icon(Icons.open_in_new, color: Colors.white),
              onPressed: _launchStore,
            ),
            SizedBox(height: AppSize.h10,),
            Text(
              context.l10n.troubleUpdating,
              textAlign: TextAlign.center,
              style: context.textTheme.bodyMedium?.copyWith(
                  fontSize: AppSize.sp12,
                  color: Colors.grey,
              ),
            ),
          ],
        ),
      ),

    );
  }
}

