// ignore_for_file: public_member_api_docs

import 'package:flutter_screenutil/flutter_screenutil.dart';

/// App size helper
class AppSize {
  const AppSize._();

  static const disableSizeUtils = false;

  static final double appPadding = 20.w;

  // Sizes in context of height
  static final double h0 = disableSizeUtils ? 0 : 0.h;
  static final double h1 = disableSizeUtils ? 1 : 1.h;
  static final double h2 = disableSizeUtils ? 2 : 2.h;
  static final double h3 = disableSizeUtils ? 3 : 3.h;
  static final double h4 = disableSizeUtils ? 4 : 4.h;
  static final double h5 = disableSizeUtils ? 5 : 5.h;
  static final double h6 = disableSizeUtils ? 6 : 6.h;
  static final double h8 = disableSizeUtils ? 8 : 8.h;
  static final double h9 = disableSizeUtils ? 9 : 9.h;
  static final double h10 = disableSizeUtils ? 10 : 10.h;
  static final double h11 = disableSizeUtils ? 11 : 11.h;
  static final double h12 = disableSizeUtils ? 12 : 12.h;
  static final double h14 = disableSizeUtils ? 14 : 14.h;
  static final double h15 = disableSizeUtils ? 15 : 15.h;
  static final double h16 = disableSizeUtils ? 16 : 16.h;
  static final double h18 = disableSizeUtils ? 18 : 18.h;
  static final double h20 = disableSizeUtils ? 20 : 20.h;
  static final double h22 = disableSizeUtils ? 22 : 22.h;
  static final double h24 = disableSizeUtils ? 24 : 24.h;
  static final double h25 = disableSizeUtils ? 25 : 25.h;
  static final double h26 = disableSizeUtils ? 26 : 26.h;
  static final double h27 = disableSizeUtils ? 27 : 27.h;
  static final double h28 = disableSizeUtils ? 28 : 28.h;
  static final double h30 = disableSizeUtils ? 30 : 30.h;
  static final double h32 = disableSizeUtils ? 32 : 32.h;
  static final double h34 = disableSizeUtils ? 34 : 34.h;
  static final double h35 = disableSizeUtils ? 35 : 35.h;
  static final double h36 = disableSizeUtils ? 36 : 36.h;
  static final double h38 = disableSizeUtils ? 38 : 38.h;
  static final double h40 = disableSizeUtils ? 40 : 40.h;
  static final double h42 = disableSizeUtils ? 42 : 42.h;
  static final double h44 = disableSizeUtils ? 44 : 44.h;
  static final double h46 = disableSizeUtils ? 46 : 46.h;
  static final double h48 = disableSizeUtils ? 48 : 48.h;
  static final double h50 = disableSizeUtils ? 50 : 50.h;
  static final double h52 = disableSizeUtils ? 52 : 52.h;
  static final double h54 = disableSizeUtils ? 54 : 54.h;
  static final double h56 = disableSizeUtils ? 56 : 56.h;
  static final double h58 = disableSizeUtils ? 58 : 58.h;
  static final double h60 = disableSizeUtils ? 60 : 60.h;
  static final double h62 = disableSizeUtils ? 62 : 62.h;
  static final double h64 = disableSizeUtils ? 64 : 64.h;
  static final double h66 = disableSizeUtils ? 66 : 66.h;
  static final double h68 = disableSizeUtils ? 68 : 68.h;

  static final double h70 = disableSizeUtils ? 70 : 70.h;
  static final double h72 = disableSizeUtils ? 72 : 72.h;
  static final double h80 = disableSizeUtils ? 80 : 80.h;
  static final double h82 = disableSizeUtils ? 82 : 82.h;
  static final double h86 = disableSizeUtils ? 86 : 86.h;
  static final double h88 = disableSizeUtils ? 88 : 88.h;
  static final double h90 = disableSizeUtils ? 90 : 90.h;
  static final double h92 = disableSizeUtils ? 92 : 92.h;
  static final double h96 = disableSizeUtils ? 96 : 96.h;
  static final double h98 = disableSizeUtils ? 98 : 98.h;
  static final double h100 = disableSizeUtils ? 100 : 100.h;
  static final double h104 = disableSizeUtils ? 104 : 104.h;
  static final double h106 = disableSizeUtils ? 106 : 106.h;
  static final double h110 = disableSizeUtils ? 110 : 110.h;
  static final double h114 = disableSizeUtils ? 114 : 114.h;
  static final double h120 = disableSizeUtils ? 120 : 120.h;
  static final double h136 = disableSizeUtils ? 136 : 136.h;
  static final double h137 = disableSizeUtils ? 137 : 137.h;
  static final double h138 = disableSizeUtils ? 138 : 138.h;
  static final double h139 = disableSizeUtils ? 139 : 139.h;
  static final double h130 = disableSizeUtils ? 130 : 130.h;
  static final double h140 = disableSizeUtils ? 140 : 140.h;
  static final double h150 = disableSizeUtils ? 150 : 150.h;
  static final double h155 = disableSizeUtils ? 155 : 155.h;
  static final double h160 = disableSizeUtils ? 160 : 160.h;
  static final double h162 = disableSizeUtils ? 162 : 162.h;
  static final double h164 = disableSizeUtils ? 164 : 164.h;
  static final double h166 = disableSizeUtils ? 166 : 166.h;
  static final double h170 = disableSizeUtils ? 170 : 170.h;
  static final double h172 = disableSizeUtils ? 172 : 172.h;
  static final double h180 = disableSizeUtils ? 180 : 180.h;
  static final double h190 = disableSizeUtils ? 190 : 190.h;
  static final double h192 = disableSizeUtils ? 192 : 192.h;
  static final double h194 = disableSizeUtils ? 194 : 194.h;
  static final double h200 = disableSizeUtils ? 200 : 200.h;
  static final double h206 = disableSizeUtils ? 206 : 206.h;
  static final double h212 = disableSizeUtils ? 212 : 212.h;
  static final double h220 = disableSizeUtils ? 220 : 220.h;
  static final double h230 = disableSizeUtils ? 230 : 230.h;
  static final double h240 = disableSizeUtils ? 240 : 240.h;
  static final double h250 = disableSizeUtils ? 250 : 250.h;
  static final double h270 = disableSizeUtils ? 270 : 270.h;
  static final double h280 = disableSizeUtils ? 280 : 280.h;
  static final double h300 = disableSizeUtils ? 300 : 300.h;
  static final double h330 = disableSizeUtils ? 330 : 330.h;
  static final double h380 = disableSizeUtils ? 380 : 380.h;
  static final double h400 = disableSizeUtils ? 400 : 400.h;
  static final double h500 = disableSizeUtils ? 500 : 500.h;

  // Sizes in context of width
  static final double w0 = disableSizeUtils ? 0 : 0.w;
  static final double w1 = disableSizeUtils ? 1 : 1.w;
  static final double w2 = disableSizeUtils ? 2 : 2.w;
  static final double w3 = disableSizeUtils ? 3 : 3.w;
  static final double w4 = disableSizeUtils ? 4 : 4.w;
  static final double w5 = disableSizeUtils ? 5 : 5.w;
  static final double w6 = disableSizeUtils ? 6 : 6.w;
  static final double w8 = disableSizeUtils ? 8 : 8.w;
  static final double w10 = disableSizeUtils ? 10 : 10.w;
  static final double w12 = disableSizeUtils ? 12 : 12.w;
  static final double w14 = disableSizeUtils ? 14 : 14.w;
  static final double w16 = disableSizeUtils ? 16 : 16.w;
  static final double w18 = disableSizeUtils ? 18 : 18.w;
  static final double w20 = disableSizeUtils ? 20 : 20.w;
  static final double w22 = disableSizeUtils ? 22 : 22.w;
  static final double w24 = disableSizeUtils ? 24 : 24.w;
  static final double w26 = disableSizeUtils ? 26 : 26.w;
  static final double w28 = disableSizeUtils ? 28 : 28.w;
  static final double w30 = disableSizeUtils ? 30 : 30.w;
  static final double w32 = disableSizeUtils ? 32 : 32.w;
  static final double w34 = disableSizeUtils ? 34 : 34.w;
  static final double w35 = disableSizeUtils ? 35 : 35.w;
  static final double w36 = disableSizeUtils ? 36 : 36.w;
  static final double w38 = disableSizeUtils ? 38 : 38.w;
  static final double w40 = disableSizeUtils ? 40 : 40.w;
  static final double w42 = disableSizeUtils ? 42 : 42.w;
  static final double w44 = disableSizeUtils ? 44 : 44.w;
  static final double w46 = disableSizeUtils ? 46 : 46.w;
  static final double w48 = disableSizeUtils ? 48 : 48.w;
  static final double w50 = disableSizeUtils ? 50 : 50.w;
  static final double w52 = disableSizeUtils ? 52 : 52.w;
  static final double w56 = disableSizeUtils ? 56 : 56.w;
  static final double w58 = disableSizeUtils ? 58 : 58.w;
  static final double w60 = disableSizeUtils ? 60 : 60.w;
  static final double w64 = disableSizeUtils ? 64 : 64.w;
  static final double w62 = disableSizeUtils ? 62 : 62.w;
  static final double w68 = disableSizeUtils ? 68 : 68.w;
  static final double w70 = disableSizeUtils ? 70 : 70.w;
  static final double w72 = disableSizeUtils ? 72 : 72.w;
  static final double w74 = disableSizeUtils ? 74 : 74.w;
  static final double w80 = disableSizeUtils ? 80 : 80.w;
  static final double w86 = disableSizeUtils ? 86 : 86.w;
  static final double w82 = disableSizeUtils ? 82 : 82.w;
  static final double w90 = disableSizeUtils ? 90 : 90.w;
  static final double w100 = disableSizeUtils ? 100 : 100.w;
  static final double w105 = disableSizeUtils ? 105 : 105.w;
  static final double w110 = disableSizeUtils ? 110 : 110.w;
  static final double w120 = disableSizeUtils ? 120 : 120.w;
  static final double w130 = disableSizeUtils ? 130 : 130.w;
  static final double w140 = disableSizeUtils ? 140 : 140.w;
  static final double w144 = disableSizeUtils ? 144 : 144.w;
  static final double w146 = disableSizeUtils ? 146 : 146.w;
  static final double w142 = disableSizeUtils ? 142 : 142.w;
  static final double w150 = disableSizeUtils ? 150 : 150.w;
  static final double w156 = disableSizeUtils ? 156 : 156.w;
  static final double w160 = disableSizeUtils ? 160 : 160.w;
  static final double w166 = disableSizeUtils ? 166 : 166.w;
  static final double w170 = disableSizeUtils ? 170 : 170.w;
  static final double w172 = disableSizeUtils ? 172 : 172.w;
  static final double w174 = disableSizeUtils ? 174 : 174.w;
  static final double w176 = disableSizeUtils ? 176 : 176.w;
  static final double w178 = disableSizeUtils ? 178 : 178.w;
  static final double w180 = disableSizeUtils ? 180 : 180.w;
  static final double w186 = disableSizeUtils ? 186 : 186.w;
  static final double w190 = disableSizeUtils ? 190 : 190.w;
  static final double w200 = disableSizeUtils ? 200 : 200.w;
  static final double w230 = disableSizeUtils ? 230 : 230.w;
  static final double w240 = disableSizeUtils ? 240 : 240.w;
  static final double w250 = disableSizeUtils ? 250 : 250.w;
  static final double w280 = disableSizeUtils ? 280 : 280.w;
  static final double w300 = disableSizeUtils ? 300 : 300.w;

  // Sizes for text
  static double sp1 = disableSizeUtils ? 1 : 1.sp;
  static final double sp2 = disableSizeUtils ? 2 : 2.sp;
  static final double sp3 = disableSizeUtils ? 3 : 3.sp;
  static final double sp4 = disableSizeUtils ? 4 : 4.sp;
  static final double sp5 = disableSizeUtils ? 5 : 5.sp;
  static final double sp6 = disableSizeUtils ? 6 : 6.sp;
  static final double sp8 = disableSizeUtils ? 8 : 8.sp;
  static final double sp7 = disableSizeUtils ? 7 : 7.sp;
  static final double sp9 = disableSizeUtils ? 9 : 9.sp;
  static final double sp10 = disableSizeUtils ? 10 : 10.sp;
  static final double sp11 = disableSizeUtils ? 11 : 11.sp;
  static final double sp12 = disableSizeUtils ? 12 : 12.sp;
  static final double sp13 = disableSizeUtils ? 13 : 13.sp;
  static final double sp14 = disableSizeUtils ? 14 : 14.sp;
  static final double sp15 = disableSizeUtils ? 15 : 15.sp;
  static final double sp16 = disableSizeUtils ? 16 : 16.sp;
  static final double sp17 = disableSizeUtils ? 17 : 17.sp;
  static final double sp18 = disableSizeUtils ? 18 : 18.sp;
  static final double sp19 = disableSizeUtils ? 19 : 19.sp;
  static final double sp20 = disableSizeUtils ? 20 : 20.sp;
  static final double sp22 = disableSizeUtils ? 22 : 22.sp;
  static final double sp24 = disableSizeUtils ? 24 : 24.sp;
  static final double sp25 = disableSizeUtils ? 25 : 25.sp;
  static final double sp26 = disableSizeUtils ? 26 : 26.sp;
  static final double sp27 = disableSizeUtils ? 27 : 27.sp;
  static final double sp28 = disableSizeUtils ? 28 : 28.sp;
  static final double sp30 = disableSizeUtils ? 30 : 30.sp;
  static final double sp32 = disableSizeUtils ? 32 : 32.sp;
  static final double sp33 = disableSizeUtils ? 33 : 33.sp;
  static final double sp34 = disableSizeUtils ? 34 : 34.sp;
  static final double sp36 = disableSizeUtils ? 36 : 36.sp;
  static final double sp38 = disableSizeUtils ? 38 : 38.sp;
  static final double sp40 = disableSizeUtils ? 40 : 40.sp;
  static final double sp42 = disableSizeUtils ? 42 : 42.sp;
  static final double sp45 = disableSizeUtils ? 45 : 45.sp;
  static final double sp48 = disableSizeUtils ? 48 : 48.sp;
  static final double sp50 = disableSizeUtils ? 50 : 50.sp;
  static final double sp52 = disableSizeUtils ? 52 : 52.sp;
  static final double sp54 = disableSizeUtils ? 54 : 54.sp;
  static final double sp56 = disableSizeUtils ? 56 : 56.sp;
  static final double sp57 = disableSizeUtils ? 57 : 57.sp;
  static final double sp60 = disableSizeUtils ? 60 : 60.sp;
  static final double sp64 = disableSizeUtils ? 64 : 64.sp;
  static final double sp72 = disableSizeUtils ? 72 : 72.sp;
  static final double sp80 = disableSizeUtils ? 80 : 80.sp;
  static final double sp100 = disableSizeUtils ? 100 : 100.sp;
  static final double sp120 = disableSizeUtils ? 120 : 120.sp;
  static final double sp140 = disableSizeUtils ? 140 : 140.sp;
  static final double sp160 = disableSizeUtils ? 160 : 160.sp;
  static final double sp340 = disableSizeUtils ? 340 : 340.sp;

  // Sizes for radius
  static final double r1 = disableSizeUtils ? 1 : 1.r;
  static final double r2 = disableSizeUtils ? 2 : 2.r;
  static final double r3 = disableSizeUtils ? 3 : 3.r;
  static final double r4 = disableSizeUtils ? 4 : 4.r;
  static final double r5 = disableSizeUtils ? 5 : 5.r;
  static final double r6 = disableSizeUtils ? 6 : 6.r;
  static final double r7 = disableSizeUtils ? 7 : 7.r;
  static final double r8 = disableSizeUtils ? 8 : 8.r;
  static final double r9 = disableSizeUtils ? 9 : 9.r;
  static final double r10 = disableSizeUtils ? 10 : 10.r;
  static final double r11 = disableSizeUtils ? 11 : 11.r;
  static final double r12 = disableSizeUtils ? 12 : 12.r;
  static final double r14 = disableSizeUtils ? 14 : 14.r;
  static final double r15 = disableSizeUtils ? 15 : 15.r;
  static final double r16 = disableSizeUtils ? 16 : 16.r;
  static final double r18 = disableSizeUtils ? 18 : 18.r;
  static final double r19 = disableSizeUtils ? 19 : 19.r;
  static final double r20 = disableSizeUtils ? 20 : 20.r;
  static final double r22 = disableSizeUtils ? 22 : 22.r;
  static final double r24 = disableSizeUtils ? 24 : 24.r;
  static final double r25 = disableSizeUtils ? 25 : 25.r;
  static final double r26 = disableSizeUtils ? 26 : 26.r;
  static final double r28 = disableSizeUtils ? 28 : 28.r;
  static final double r30 = disableSizeUtils ? 30 : 30.r;
  static final double r31 = disableSizeUtils ? 31 : 31.r;
  static final double r32 = disableSizeUtils ? 32 : 32.r;
  static final double r34 = disableSizeUtils ? 34 : 34.r;
  static final double r36 = disableSizeUtils ? 36 : 36.r;
  static final double r38 = disableSizeUtils ? 38 : 38.r;
  static final double r40 = disableSizeUtils ? 40 : 40.r;
  static final double r44 = disableSizeUtils ? 44 : 44.r;
  static final double r45 = disableSizeUtils ? 45 : 45.r;
  static final double r46 = disableSizeUtils ? 46 : 46.r;
  static final double r48 = disableSizeUtils ? 48 : 48.r;
  static final double r50 = disableSizeUtils ? 50 : 50.r;
  static final double r52 = disableSizeUtils ? 52 : 52.r;
  static final double r54 = disableSizeUtils ? 54 : 54.r;
  static final double r56 = disableSizeUtils ? 56 : 56.r;
  static final double r58 = disableSizeUtils ? 58 : 58.r;
  static final double r60 = disableSizeUtils ? 60 : 60.r;
  static final double r100 = disableSizeUtils ? 100 : 100.r;
  static final double r200 = disableSizeUtils ? 200 : 200.r;
  static final double r315 = disableSizeUtils ? 315 : 315.r;
  static final double rButton = disableSizeUtils ? 10 : 10.r;
  static final double rBgContainer = disableSizeUtils ? 10 : 10.r;
  static final double rTextField = disableSizeUtils ? 10 : 10.r;
  static final double rBottomSheet = disableSizeUtils ? 20 : 20.r;

  // Sizes for specific Widgets
  static final double buttonHeight = disableSizeUtils ? 48 : 36.h;
  static final double minButtonWidth = disableSizeUtils ? 150 : 150.w;

  static final double borderRadius = disableSizeUtils ? 12 : 12.r;
}
