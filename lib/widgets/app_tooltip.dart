import 'package:flutter/material.dart';
import 'package:info_popup/info_popup.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

class AppTooltip extends StatelessWidget {
  const AppTooltip({super.key, required this.msg});

  final String msg;

  @override
  Widget build(BuildContext context) {
    return InfoPopupWidget(
      arrowTheme:
          const InfoPopupArrowTheme(arrowDirection: ArrowDirection.down),
      contentTitle: msg,
      // contentMaxWidth: context.width * 0.5,
      contentTheme: InfoPopupContentTheme(
        infoContainerBackgroundColor: AppColors.black,
        infoTextStyle: context.textTheme.bodyMedium!.copyWith(
          color: AppColors.white,
          fontSize: AppSize.sp12,
        ),
        contentPadding: EdgeInsets.all(AppSize.sp8),
        contentBorderRadius: BorderRadius.circular(AppSize.r8),
      ),
      areaBackgroundColor: AppColors.transparent,
      indicatorOffset: Offset.zero,
      contentOffset: Offset.zero,
      enableLog: true,
      // onControllerCreated: (controller) {
      //   'Info Popup Controller Created'.logD;
      // },
      // onAreaPressed: (InfoPopupController controller) {
      //   'Area Pressed'.logD;
      // },
      // infoPopupDismissed: () {
      //   'Info Popup Dismissed'.logD;
      // },
      // onLayoutMounted: (Size size) {
      //   'Info Popup Layout Mounted'.logD;
      // },
      child: const Icon(Icons.info, size: 25, color: AppColors.primaryColor),
    );
  }
}
