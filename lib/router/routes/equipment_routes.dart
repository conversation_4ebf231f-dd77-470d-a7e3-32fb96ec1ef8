import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/equipment_page.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/add_and_update_equipment_page.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/models/equipment_add_update_params.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';

class EquipmentRoutes {
  static StatefulShellBranch buildEquipmentBranch() {
    return StatefulShellBranch(
      routes: [
        GoRoute(
          path: AppRoutes.equipmentBase,
          builder: (context, state) => const EquipmentPage(),
        ),
      ],
    );
  }

  static List<GoRoute> buildNestedEquipmentRoutes() {
    return [
      GoRoute(
        path: AppRoutes.equipmentAddUpdatePath,
        name: AppRoutes.equipmentAddUpdateScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as EquipmentAddUpdateParams;
          return AddAndUpdateEquipmentPage(
            equipmentDataParams: params,
          );
        },
      ),
    ];
  }
}
