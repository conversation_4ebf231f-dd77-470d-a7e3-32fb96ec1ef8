import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/cancel_trip_list_page/cancel_trip_page.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/customer_support_page/customer_support_page.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/driver_list_page_for_provider/driver_list_page_for_provider.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/driver_list_page_for_provider/pages/add_driver_page/add_driver_page.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/edit_profile_page/edit_profile_page.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/profile_saved_route_page/profile_saved_route_page.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/set_new_password_page/set_new_password_page.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/profile_page.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';

class ProfileRoutes {
  static StatefulShellBranch buildProfileBranch() {
    return StatefulShellBranch(
      routes: [
        GoRoute(
          path: AppRoutes.profileBase,
          builder: (context, state) => const ProfilePage(),
        ),
      ],
    );
  }

  static List<GoRoute> buildNestedProfileRoutes() {
    return [
      GoRoute(
        path: AppRoutes.profileEditPath,
        name: AppRoutes.profileEditScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const EditProfilePage(),
      ),
      GoRoute(
        path: AppRoutes.profileSavedRoutePath,
        name: AppRoutes.profileSavedRouteScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const ProfileSavedRoutePage(),
      ),
      GoRoute(
        path: AppRoutes.profileSetNewPasswordPath,
        name: AppRoutes.profileSetNewPasswordScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          return const SetNewPasswordPage();
        },
      ),
      GoRoute(
        path: AppRoutes.profileCustomerSupportPath,
        name: AppRoutes.profileCustomerSupportScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const CustomerSupportPage(),
      ),
      GoRoute(
        path: AppRoutes.profileCancelTripListPath,
        name: AppRoutes.profileCancelTripListScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const CancelTripPage(),
      ),
      GoRoute(
        path: AppRoutes.profileDriverListPath,
        name: AppRoutes.profileDriverListScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const DriverListPageForProvider(),
      ),
      GoRoute(
        path: AppRoutes.profileDriverAddPath,
        name: AppRoutes.profileDriverAddScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const AddDriverPage(),
      ),
    ];
  }
}
